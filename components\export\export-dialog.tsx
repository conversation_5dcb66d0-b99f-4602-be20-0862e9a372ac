"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, File, Globe, BookOpen, Loader2 } from "lucide-react"
import { exportManager } from "@/lib/export"
import type { Note, ExportOptions } from "@/lib/types"

interface ExportDialogProps {
  notes: Note[]
  isOpen: boolean
  onClose: () => void
}

export function ExportDialog({ notes, isOpen, onClose }: ExportDialogProps) {
  const [selectedNotes, setSelectedNotes] = useState<string[]>([])
  const [exportFormat, setExportFormat] = useState<"pdf" | "docx" | "html" | "markdown">("pdf")
  const [includeMetadata, setIncludeMetadata] = useState(true)
  const [includeComments, setIncludeComments] = useState(false)
  const [isExporting, setIsExporting] = useState(false)

  const handleExport = async () => {
    if (selectedNotes.length === 0) return

    setIsExporting(true)

    try {
      const exportOptions: ExportOptions = {
        format: exportFormat,
        includeMetadata,
        includeComments,
      }

      const notesToExport = notes.filter((note) => selectedNotes.includes(note.id))

      if (notesToExport.length === 1) {
        // Single note export
        const note = notesToExport[0]
        const blob = await exportManager.exportNote(note, exportOptions)
        const filename = `${note.title.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.${exportFormat}`
        exportManager.downloadBlob(blob, filename)
      } else {
        // Multiple notes export
        const blob = await exportManager.exportMultipleNotes(notesToExport, exportOptions)
        const filename = `notes_export_${new Date().toISOString().split("T")[0]}.${exportFormat}`
        exportManager.downloadBlob(blob, filename)
      }

      onClose()
    } catch (error) {
      console.error("[v0] Export error:", error)
    } finally {
      setIsExporting(false)
    }
  }

  const toggleNoteSelection = (noteId: string) => {
    setSelectedNotes((prev) => (prev.includes(noteId) ? prev.filter((id) => id !== noteId) : [...prev, noteId]))
  }

  const selectAllNotes = () => {
    setSelectedNotes(notes.map((note) => note.id))
  }

  const clearSelection = () => {
    setSelectedNotes([])
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case "pdf":
        return <FileText className="w-4 h-4" />
      case "docx":
        return <File className="w-4 h-4" />
      case "html":
        return <Globe className="w-4 h-4" />
      case "markdown":
        return <BookOpen className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getTypeIcon = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return <FileText className="w-4 h-4" />
      case "markdown":
        return <BookOpen className="w-4 h-4" />
      case "kanban":
        return <FileText className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "markdown":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "kanban":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export Notes
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Export Format</CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      PDF Document
                    </div>
                  </SelectItem>
                  <SelectItem value="docx">
                    <div className="flex items-center gap-2">
                      <File className="w-4 h-4" />
                      Word Document
                    </div>
                  </SelectItem>
                  <SelectItem value="html">
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      HTML File
                    </div>
                  </SelectItem>
                  <SelectItem value="markdown">
                    <div className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      Markdown File
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Export Options */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Export Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox id="include-metadata" checked={includeMetadata} onCheckedChange={setIncludeMetadata} />
                <Label htmlFor="include-metadata">Include metadata (creation date, tags, etc.)</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="include-comments" checked={includeComments} onCheckedChange={setIncludeComments} />
                <Label htmlFor="include-comments">Include comments and collaboration history</Label>
              </div>
            </CardContent>
          </Card>

          {/* Note Selection */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">
                  Select Notes ({selectedNotes.length} of {notes.length})
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={selectAllNotes}>
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearSelection}>
                    Clear
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {notes.map((note) => (
                  <div
                    key={note.id}
                    className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedNotes.includes(note.id) ? "bg-primary/10 border-primary" : "hover:bg-muted/50"
                    }`}
                    onClick={() => toggleNoteSelection(note.id)}
                  >
                    <Checkbox
                      checked={selectedNotes.includes(note.id)}
                      onChange={() => {}} // Handled by parent click
                    />
                    <div className="flex items-center gap-2">
                      {getTypeIcon(note.type)}
                      <Badge variant="secondary" className={`text-xs ${getTypeColor(note.type)}`}>
                        {note.type}
                      </Badge>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">{note.title}</h4>
                      <p className="text-xs text-muted-foreground">
                        Updated {new Date(note.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Export Button */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={selectedNotes.length === 0 || isExporting}>
              {isExporting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  {getFormatIcon(exportFormat)}
                  <span className="ml-2">
                    Export {selectedNotes.length} Note{selectedNotes.length !== 1 ? "s" : ""}
                  </span>
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
