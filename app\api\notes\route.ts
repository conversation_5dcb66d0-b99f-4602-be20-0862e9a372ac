import { NextResponse } from "next/server"
import { z } from "zod"
import { Database, type Note } from "@/lib/database"
import { withAuth, type AuthenticatedRequest } from "@/lib/middleware-auth"

const createNoteSchema = z.object({
  title: z.string().min(1, "Title is required").max(500, "Title too long"),
  content: z.string().default(""),
  type: z.enum(["rich-text", "markdown", "kanban"]).default("rich-text"),
  folder_id: z.number().optional(),
  tags: z.array(z.string()).default([]),
})

const searchSchema = z.object({
  q: z.string().optional(),
  type: z.enum(["rich-text", "markdown", "kanban"]).optional(),
  tags: z.string().optional(), // comma-separated tags
  folder_id: z.string().optional(),
  limit: z.string().default("20"),
  offset: z.string().default("0"),
})

// GET /api/notes - Get user's notes with optional search
export const GET = withAuth(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const params = searchSchema.parse(Object.fromEntries(searchParams))

    let sql = `
      SELECT n.*, f.name as folder_name 
      FROM notes n 
      LEFT JOIN folders f ON n.folder_id = f.id 
      WHERE n.owner_id = ?
    `
    const queryParams: any[] = [request.user.userId]

    // Add search conditions
    if (params.q) {
      sql +=
        " AND (MATCH(n.title, n.content) AGAINST(? IN NATURAL LANGUAGE MODE) OR n.title LIKE ? OR n.content LIKE ?)"
      queryParams.push(params.q, `%${params.q}%`, `%${params.q}%`)
    }

    if (params.type) {
      sql += " AND n.type = ?"
      queryParams.push(params.type)
    }

    if (params.folder_id) {
      sql += " AND n.folder_id = ?"
      queryParams.push(Number.parseInt(params.folder_id))
    }

    if (params.tags) {
      const tags = params.tags.split(",").map((tag) => tag.trim())
      sql += " AND JSON_OVERLAPS(n.tags, ?)"
      queryParams.push(JSON.stringify(tags))
    }

    sql += " ORDER BY n.updated_at DESC LIMIT ? OFFSET ?"
    queryParams.push(Number.parseInt(params.limit), Number.parseInt(params.offset))

    const notes = await Database.query<Note & { folder_name?: string }>(sql, queryParams)

    // Parse JSON tags for each note
    const notesWithParsedTags = notes.map((note) => ({
      ...note,
      tags: typeof note.tags === "string" ? JSON.parse(note.tags) : note.tags || [],
    }))

    return NextResponse.json({ notes: notesWithParsedTags })
  } catch (error) {
    console.error("Get notes error:", error)
    return NextResponse.json({ error: "Failed to fetch notes" }, { status: 500 })
  }
})

// POST /api/notes - Create new note
export const POST = withAuth(async (request: AuthenticatedRequest) => {
  try {
    const body = await request.json()
    const { title, content, type, folder_id, tags } = createNoteSchema.parse(body)

    const noteId = await Database.insert(
      "INSERT INTO notes (title, content, type, owner_id, folder_id, tags) VALUES (?, ?, ?, ?, ?, ?)",
      [title, content, type, request.user.userId, folder_id || null, JSON.stringify(tags)],
    )

    // Log activity
    await Database.insert("INSERT INTO activity_log (note_id, user_id, action, details) VALUES (?, ?, ?, ?)", [
      noteId,
      request.user.userId,
      "created",
      JSON.stringify({ title, type }),
    ])

    const note = await Database.queryOne<Note>("SELECT * FROM notes WHERE id = ?", [noteId])

    if (note) {
      // Parse JSON tags
      note.tags = typeof note.tags === "string" ? JSON.parse(note.tags) : note.tags || []
    }

    return NextResponse.json({ note, message: "Note created successfully" }, { status: 201 })
  } catch (error) {
    console.error("Create note error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create note" }, { status: 500 })
  }
})
