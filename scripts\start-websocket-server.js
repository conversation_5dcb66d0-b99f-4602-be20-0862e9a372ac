const { createServer } = require('http');
const { WebSocketServer } = require('ws');

const WS_PORT = Number(process.env.WS_PORT) || 3001;

console.log("Starting WebSocket server...");

// Create HTTP server
const server = createServer();

// Create WebSocket server
const wss = new WebSocketServer({ server });

// Store connections
const connections = new Map();
const noteRooms = new Map();

wss.on('connection', (ws, request) => {
  console.log('New WebSocket connection');

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('Received message:', message);
      
      // Echo the message back for now (basic implementation)
      ws.send(JSON.stringify({
        type: 'echo',
        data: message,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('WebSocket message error:', error);
      ws.send(JSON.stringify({ type: 'error', error: 'Invalid message format' }));
    }
  });

  ws.on('close', () => {
    console.log('WebSocket connection closed');
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to WebSocket server',
    timestamp: Date.now()
  }));
});

// Start the server
server.listen(WS_PORT, () => {
  console.log(`WebSocket server started on port ${WS_PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down WebSocket server...');
  wss.close(() => {
    server.close(() => {
      console.log('WebSocket server stopped');
      process.exit(0);
    });
  });
});
