"use client"

interface CollaborationMessage {
  type: "join" | "leave" | "cursor" | "content" | "user-list" | "ping" | "pong" | "error"
  noteId?: number
  userId?: number
  data?: any
  timestamp?: number
  error?: string
}

interface CollaborationUser {
  userId: number
  name: string
  email: string
  avatar_url?: string
  cursor_position: any
  last_seen: Date
  is_active: boolean
}

export class WebSocketClient {
  private ws: WebSocket | null = null
  private noteId: number | null = null
  private token: string | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private pingInterval: NodeJS.Timeout | null = null

  // Event handlers
  public onUserListUpdate: ((users: CollaborationUser[]) => void) | null = null
  public onCursorUpdate: ((userId: number, position: any) => void) | null = null
  public onContentUpdate: ((userId: number, content: any) => void) | null = null
  public onError: ((error: string) => void) | null = null
  public onConnectionChange: ((connected: boolean) => void) | null = null

  constructor() {
    // Auto-reconnect on page visibility change
    if (typeof document !== "undefined") {
      document.addEventListener("visibilitychange", () => {
        if (!document.hidden && this.noteId && this.token) {
          this.connect(this.noteId, this.token)
        }
      })
    }
  }

  connect(noteId: number, token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.noteId = noteId
      this.token = token

      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || "ws://localhost:3001"

      try {
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log("WebSocket connected")
          this.reconnectAttempts = 0
          this.onConnectionChange?.(true)

          // Join the note room
          this.send({
            type: "join",
            noteId,
            data: { token },
          })

          // Start ping interval
          this.startPingInterval()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: CollaborationMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error("Failed to parse WebSocket message:", error)
          }
        }

        this.ws.onclose = () => {
          console.log("WebSocket disconnected")
          this.onConnectionChange?.(false)
          this.stopPingInterval()
          this.attemptReconnect()
        }

        this.ws.onerror = (error) => {
          console.error("WebSocket error:", error)
          this.onError?.("Connection error")
          reject(error)
        }
      } catch (error) {
        console.error("Failed to create WebSocket:", error)
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.ws) {
      this.send({ type: "leave", noteId: this.noteId! })
      this.ws.close()
      this.ws = null
    }
    this.stopPingInterval()
    this.noteId = null
    this.token = null
  }

  sendCursorUpdate(position: any) {
    this.send({
      type: "cursor",
      noteId: this.noteId!,
      data: position,
    })
  }

  sendContentUpdate(content: any) {
    this.send({
      type: "content",
      noteId: this.noteId!,
      data: content,
    })
  }

  private send(message: CollaborationMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    }
  }

  private handleMessage(message: CollaborationMessage) {
    switch (message.type) {
      case "user-list":
        this.onUserListUpdate?.(message.data || [])
        break
      case "cursor":
        if (message.userId) {
          this.onCursorUpdate?.(message.userId, message.data)
        }
        break
      case "content":
        if (message.userId) {
          this.onContentUpdate?.(message.userId, message.data)
        }
        break
      case "error":
        this.onError?.(message.error || "Unknown error")
        break
      case "ping":
        // Respond to server ping
        this.send({ type: "pong", timestamp: Date.now() })
        break
      case "pong":
        // Server responded to our ping
        break
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("Max reconnection attempts reached")
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`)

    setTimeout(() => {
      if (this.noteId && this.token) {
        this.connect(this.noteId, this.token).catch(() => {
          // Reconnection failed, will try again
        })
      }
    }, delay)
  }

  private startPingInterval() {
    this.stopPingInterval()
    this.pingInterval = setInterval(() => {
      this.send({ type: "ping", timestamp: Date.now() })
    }, 30000)
  }

  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
  }

  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
}
