import type { NextRequest } from "next/server"
import { AuthUtils, type JWTPayload } from "./auth-utils"

export interface AuthenticatedRequest extends NextRequest {
  user: JWTPayload
}

export function withAuth(handler: (req: AuthenticatedRequest) => Promise<Response>) {
  return async (request: NextRequest): Promise<Response> => {
    try {
      const authHeader = request.headers.get("authorization")
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return new Response(JSON.stringify({ error: "Authorization token required" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        })
      }

      const token = authHeader.substring(7)
      const payload = AuthUtils.verifyToken(token)
      if (!payload) {
        return new Response(JSON.stringify({ error: "Invalid or expired token" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        })
      }

      // Add user to request object
      const authenticatedRequest = request as AuthenticatedRequest
      authenticatedRequest.user = payload

      return handler(authenticatedRequest)
    } catch (error) {
      console.error("Auth middleware error:", error)
      return new Response(JSON.stringify({ error: "Authentication failed" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      })
    }
  }
}

export function getAuthUser(request: NextRequest): JWTPayload | null {
  try {
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null
    }

    const token = authHeader.substring(7)
    return AuthUtils.verifyToken(token)
  } catch {
    return null
  }
}
