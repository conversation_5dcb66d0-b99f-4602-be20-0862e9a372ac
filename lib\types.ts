export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  createdAt: Date
}

export interface Note {
  id: string
  title: string
  content: string
  type: "rich-text" | "markdown" | "kanban"
  authorId: string
  collaborators: string[]
  isPublic: boolean
  shareSettings: ShareSettings
  tags: string[]
  createdAt: Date
  updatedAt: Date
  version: number
}

export interface ShareSettings {
  isPublic: boolean
  allowedUsers: string[]
  permissions: {
    [userId: string]: "view" | "edit" | "admin"
  }
  shareLink?: string
  expiresAt?: Date
}

export interface KanbanBoard {
  id: string
  noteId: string
  columns: KanbanColumn[]
}

export interface KanbanColumn {
  id: string
  title: string
  cards: KanbanCard[]
  order: number
}

export interface KanbanCard {
  id: string
  title: string
  description?: string
  assignedTo?: string
  dueDate?: Date
  priority: "low" | "medium" | "high"
  order: number
}

export interface CollaborationSession {
  noteId: string
  activeUsers: {
    userId: string
    cursor?: { line: number; column: number }
    selection?: { start: number; end: number }
  }[]
  changes: Change[]
}

export interface Change {
  id: string
  userId: string
  type: "insert" | "delete" | "format"
  position: number
  content: string
  timestamp: Date
}

export interface SearchResult {
  noteId: string
  title: string
  snippet: string
  relevance: number
}

export interface ExportOptions {
  format: "pdf" | "docx" | "html" | "markdown"
  includeMetadata: boolean
  includeComments: boolean
}
