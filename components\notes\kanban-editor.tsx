"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Trash2, Edit } from "lucide-react"
import type { Note, KanbanColumn, KanbanCard } from "@/lib/types"
import Kanban from "@/components/icons/kanban" // Declare the Kanban variable

interface KanbanEditorProps {
  note: Note
  onContentChange: (content: string) => void
}

interface KanbanData {
  columns: KanbanColumn[]
}

export function KanbanEditor({ note, onContentChange }: KanbanEditorProps) {
  const [kanbanData, setKanbanData] = useState<KanbanData>({ columns: [] })
  const [newColumnTitle, setNewColumnTitle] = useState("")
  const [editingCard, setEditingCard] = useState<{ columnId: string; cardId: string } | null>(null)

  useEffect(() => {
    try {
      const data = note.content ? JSON.parse(note.content) : { columns: [] }
      setKanbanData(data)
    } catch (error) {
      console.error("Failed to parse kanban data:", error)
      setKanbanData({ columns: [] })
    }
  }, [note.content])

  const updateKanbanData = (newData: KanbanData) => {
    setKanbanData(newData)
    onContentChange(JSON.stringify(newData))
  }

  const addColumn = () => {
    if (!newColumnTitle.trim()) return

    const newColumn: KanbanColumn = {
      id: crypto.randomUUID(),
      title: newColumnTitle,
      cards: [],
      order: kanbanData.columns.length,
    }

    updateKanbanData({
      columns: [...kanbanData.columns, newColumn],
    })
    setNewColumnTitle("")
  }

  const addCard = (columnId: string) => {
    const newCard: KanbanCard = {
      id: crypto.randomUUID(),
      title: "New Task",
      description: "",
      priority: "medium",
      order: 0,
    }

    const updatedColumns = kanbanData.columns.map((column) =>
      column.id === columnId ? { ...column, cards: [...column.cards, newCard] } : column,
    )

    updateKanbanData({ columns: updatedColumns })
    setEditingCard({ columnId, cardId: newCard.id })
  }

  const updateCard = (columnId: string, cardId: string, updates: Partial<KanbanCard>) => {
    const updatedColumns = kanbanData.columns.map((column) =>
      column.id === columnId
        ? {
            ...column,
            cards: column.cards.map((card) => (card.id === cardId ? { ...card, ...updates } : card)),
          }
        : column,
    )

    updateKanbanData({ columns: updatedColumns })
  }

  const deleteCard = (columnId: string, cardId: string) => {
    const updatedColumns = kanbanData.columns.map((column) =>
      column.id === columnId ? { ...column, cards: column.cards.filter((card) => card.id !== cardId) } : column,
    )

    updateKanbanData({ columns: updatedColumns })
  }

  const deleteColumn = (columnId: string) => {
    const updatedColumns = kanbanData.columns.filter((column) => column.id !== columnId)
    updateKanbanData({ columns: updatedColumns })
  }

  const getPriorityColor = (priority: KanbanCard["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="editor-toolbar">
        <div className="flex items-center gap-4">
          <h3 className="font-medium">Kanban Board</h3>
          <div className="flex items-center gap-2">
            <Input
              placeholder="New column name"
              value={newColumnTitle}
              onChange={(e) => setNewColumnTitle(e.target.value)}
              className="w-40"
              onKeyDown={(e) => e.key === "Enter" && addColumn()}
            />
            <Button size="sm" onClick={addColumn}>
              <Plus className="w-4 h-4 mr-1" />
              Add Column
            </Button>
          </div>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="flex gap-6 h-full">
          {kanbanData.columns.map((column) => (
            <div key={column.id} className="kanban-column">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-sm">{column.title}</h4>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" onClick={() => addCard(column.id)} className="h-6 w-6 p-0">
                    <Plus className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteColumn(column.id)}
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                {column.cards.map((card) => (
                  <Card key={card.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <h5 className="font-medium text-sm line-clamp-2">{card.title}</h5>
                        <div className="flex items-center gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <Edit className="w-3 h-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Edit Card</DialogTitle>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium">Title</label>
                                  <Input
                                    value={card.title}
                                    onChange={(e) => updateCard(column.id, card.id, { title: e.target.value })}
                                  />
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Description</label>
                                  <Textarea
                                    value={card.description || ""}
                                    onChange={(e) => updateCard(column.id, card.id, { description: e.target.value })}
                                  />
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Priority</label>
                                  <select
                                    value={card.priority}
                                    onChange={(e) =>
                                      updateCard(column.id, card.id, {
                                        priority: e.target.value as KanbanCard["priority"],
                                      })
                                    }
                                    className="w-full p-2 border rounded-md"
                                  >
                                    <option value="low">Low</option>
                                    <option value="medium">Medium</option>
                                    <option value="high">High</option>
                                  </select>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteCard(column.id, card.id)}
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    {(card.description || card.priority !== "medium") && (
                      <CardContent className="pt-0">
                        {card.description && (
                          <p className="text-xs text-muted-foreground mb-2 line-clamp-3">{card.description}</p>
                        )}
                        <Badge variant="secondary" className={`text-xs ${getPriorityColor(card.priority)}`}>
                          {card.priority}
                        </Badge>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            </div>
          ))}

          {kanbanData.columns.length === 0 && (
            <div className="flex-1 flex items-center justify-center text-center text-muted-foreground">
              <div>
                <Kanban className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No columns yet</p>
                <p className="text-sm">Add your first column to get started</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
