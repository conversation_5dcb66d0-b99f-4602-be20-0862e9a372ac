interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

class ApiClient {
  private baseUrl: string
  private token: string | null = null

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || ""
    // Load token from localStorage on initialization
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("auth_token")
    }
  }

  setToken(token: string | null) {
    this.token = token
    if (typeof window !== "undefined") {
      if (token) {
        localStorage.setItem("auth_token", token)
      } else {
        localStorage.removeItem("auth_token")
      }
    }
  }

  private async request<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}/api${endpoint}`

    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP ${response.status}`,
        }
      }

      return {
        success: true,
        data,
        message: data.message,
      }
    } catch (error) {
      console.error("API request failed:", error)
      return {
        success: false,
        error: "Network error occurred",
      }
    }
  }

  // Authentication endpoints
  async login(email: string, password: string) {
    const response = await this.request("/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    })

    if (response.success && response.data?.token) {
      this.setToken(response.data.token)
    }

    return response
  }

  async signup(email: string, password: string, name: string) {
    const response = await this.request("/auth/signup", {
      method: "POST",
      body: JSON.stringify({ email, password, name }),
    })

    if (response.success && response.data?.token) {
      this.setToken(response.data.token)
    }

    return response
  }

  async logout() {
    const response = await this.request("/auth/logout", {
      method: "POST",
    })

    this.setToken(null)
    return response
  }

  async getCurrentUser() {
    return this.request("/auth/me")
  }

  // Notes endpoints
  async getNotes(params?: {
    q?: string
    type?: string
    tags?: string
    folder_id?: string
    limit?: number
    offset?: number
  }) {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const query = searchParams.toString()
    return this.request(`/notes${query ? `?${query}` : ""}`)
  }

  async getNote(id: number) {
    return this.request(`/notes/${id}`)
  }

  async createNote(note: {
    title: string
    content?: string
    type?: "rich-text" | "markdown" | "kanban"
    folder_id?: number
    tags?: string[]
  }) {
    return this.request("/notes", {
      method: "POST",
      body: JSON.stringify(note),
    })
  }

  async updateNote(
    id: number,
    updates: {
      title?: string
      content?: string
      type?: "rich-text" | "markdown" | "kanban"
      folder_id?: number | null
      tags?: string[]
      is_favorite?: boolean
    },
  ) {
    return this.request(`/notes/${id}`, {
      method: "PUT",
      body: JSON.stringify(updates),
    })
  }

  async deleteNote(id: number) {
    return this.request(`/notes/${id}`, {
      method: "DELETE",
    })
  }

  // Sharing endpoints
  async shareNote(
    id: number,
    shareData: {
      shared_with_email?: string
      permission?: "view" | "edit" | "admin"
      expires_at?: string
      is_public?: boolean
    },
  ) {
    return this.request(`/notes/${id}/share`, {
      method: "POST",
      body: JSON.stringify(shareData),
    })
  }

  async getNoteShares(id: number) {
    return this.request(`/notes/${id}/share`)
  }

  // Folders endpoints
  async getFolders() {
    return this.request("/folders")
  }

  async createFolder(name: string, parent_id?: number) {
    return this.request("/folders", {
      method: "POST",
      body: JSON.stringify({ name, parent_id }),
    })
  }

  // Collaboration endpoints
  async getCollaborators(noteId: number) {
    return this.request(`/collaboration/${noteId}`)
  }

  // Health check
  async healthCheck() {
    return this.request("/health")
  }
}

export const apiClient = new ApiClient()
