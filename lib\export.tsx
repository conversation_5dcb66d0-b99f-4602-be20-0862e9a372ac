import type { Note, ExportOptions } from "./types"

export class ExportManager {
  async exportNote(note: Note, options: ExportOptions): Promise<Blob> {
    switch (options.format) {
      case "pdf":
        return this.exportToPDF(note, options)
      case "docx":
        return this.exportToDocx(note, options)
      case "html":
        return this.exportToHTML(note, options)
      case "markdown":
        return this.exportToMarkdown(note, options)
      default:
        throw new Error(`Unsupported export format: ${options.format}`)
    }
  }

  async exportMultipleNotes(notes: Note[], options: ExportOptions): Promise<Blob> {
    const combinedContent = notes.map((note) => this.prepareNoteContent(note, options)).join("\n\n---\n\n")

    switch (options.format) {
      case "html":
        return new Blob([this.wrapInHTMLDocument(combinedContent)], { type: "text/html" })
      case "markdown":
        return new Blob([combinedContent], { type: "text/markdown" })
      default:
        // For PDF and DOCX, export as HTML for now
        return new Blob([this.wrapInHTMLDocument(combinedContent)], { type: "text/html" })
    }
  }

  private async exportToPDF(note: Note, options: ExportOptions): Promise<Blob> {
    // In a real app, you'd use a library like jsPDF or Puppeteer
    // For now, we'll create an HTML version that can be printed to PDF
    const htmlContent = this.prepareNoteContent(note, options)
    const pdfHTML = this.wrapInHTMLDocument(htmlContent, true)

    return new Blob([pdfHTML], { type: "text/html" })
  }

  private async exportToDocx(note: Note, options: ExportOptions): Promise<Blob> {
    // In a real app, you'd use a library like docx or mammoth
    // For now, we'll create an HTML version
    const htmlContent = this.prepareNoteContent(note, options)
    const docxHTML = this.wrapInHTMLDocument(htmlContent)

    return new Blob([docxHTML], { type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document" })
  }

  private async exportToHTML(note: Note, options: ExportOptions): Promise<Blob> {
    const htmlContent = this.prepareNoteContent(note, options)
    const fullHTML = this.wrapInHTMLDocument(htmlContent)

    return new Blob([fullHTML], { type: "text/html" })
  }

  private async exportToMarkdown(note: Note, options: ExportOptions): Promise<Blob> {
    let content = ""

    // Add metadata if requested
    if (options.includeMetadata) {
      content += `---\n`
      content += `title: ${note.title}\n`
      content += `type: ${note.type}\n`
      content += `created: ${note.createdAt.toISOString()}\n`
      content += `updated: ${note.updatedAt.toISOString()}\n`
      if (note.tags.length > 0) {
        content += `tags: [${note.tags.join(", ")}]\n`
      }
      content += `---\n\n`
    }

    content += `# ${note.title}\n\n`

    switch (note.type) {
      case "markdown":
        content += note.content
        break
      case "rich-text":
        content += this.htmlToMarkdown(note.content)
        break
      case "kanban":
        content += this.kanbanToMarkdown(note.content)
        break
    }

    return new Blob([content], { type: "text/markdown" })
  }

  private prepareNoteContent(note: Note, options: ExportOptions): string {
    let content = `<h1>${this.escapeHtml(note.title)}</h1>\n`

    // Add metadata if requested
    if (options.includeMetadata) {
      content += `<div class="metadata">\n`
      content += `<p><strong>Type:</strong> ${note.type}</p>\n`
      content += `<p><strong>Created:</strong> ${note.createdAt.toLocaleString()}</p>\n`
      content += `<p><strong>Last Updated:</strong> ${note.updatedAt.toLocaleString()}</p>\n`
      if (note.tags.length > 0) {
        content += `<p><strong>Tags:</strong> ${note.tags.join(", ")}</p>\n`
      }
      content += `</div>\n\n`
    }

    // Add main content based on note type
    switch (note.type) {
      case "rich-text":
        content += note.content
        break
      case "markdown":
        content += this.markdownToHTML(note.content)
        break
      case "kanban":
        content += this.kanbanToHTML(note.content)
        break
    }

    return content
  }

  private wrapInHTMLDocument(content: string, forPrint = false): string {
    const printStyles = forPrint
      ? `
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        @page { margin: 1in; }
      </style>
    `
      : ""

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exported Note</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2d3748;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 { font-size: 2.5rem; }
        h2 { font-size: 2rem; }
        h3 { font-size: 1.5rem; }
        .metadata {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .metadata p {
            margin: 0.5rem 0;
        }
        .kanban-board {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .kanban-column {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
        }
        .kanban-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.25rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
        }
        .priority-high { border-left: 4px solid #f56565; }
        .priority-medium { border-left: 4px solid #ed8936; }
        .priority-low { border-left: 4px solid #48bb78; }
        code {
            background: #f7fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        pre {
            background: #f7fafc;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid #4299e1;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
        }
    </style>
    ${printStyles}
</head>
<body>
    ${content}
</body>
</html>
    `
  }

  private markdownToHTML(markdown: string): string {
    return markdown
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>")
      .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
      .replace(/\*(.*)\*/gim, "<em>$1</em>")
      .replace(/!\[([^\]]*)\]$$([^)]*)$$/gim, '<img alt="$1" src="$2" />')
      .replace(/\[([^\]]*)\]$$([^)]*)$$/gim, '<a href="$2">$1</a>')
      .replace(/\n$/gim, "<br />")
      .replace(/^> (.*$)/gim, "<blockquote>$1</blockquote>")
      .replace(/^\* (.*$)/gim, "<ul><li>$1</li></ul>")
      .replace(/^1\. (.*$)/gim, "<ol><li>$1</li></ol>")
      .replace(/`([^`]*)`/gim, "<code>$1</code>")
      .replace(/```([^```]*)```/gim, "<pre><code>$1</code></pre>")
  }

  private htmlToMarkdown(html: string): string {
    return html
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, "# $1\n\n")
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, "## $1\n\n")
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, "### $1\n\n")
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, "**$1**")
      .replace(/<em[^>]*>(.*?)<\/em>/gi, "*$1*")
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, "[$2]($1)")
      .replace(/<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*>/gi, "![$1]($2)")
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<\/p>/gi, "\n\n")
      .replace(/<p[^>]*>/gi, "")
      .replace(/<[^>]*>/g, "")
  }

  private kanbanToHTML(kanbanContent: string): string {
    try {
      const kanbanData = JSON.parse(kanbanContent)
      let html = '<div class="kanban-board">\n'

      kanbanData.columns?.forEach((column: any) => {
        html += `  <div class="kanban-column">\n`
        html += `    <h3>${this.escapeHtml(column.title)}</h3>\n`

        column.cards?.forEach((card: any) => {
          html += `    <div class="kanban-card priority-${card.priority || "medium"}">\n`
          html += `      <h4>${this.escapeHtml(card.title)}</h4>\n`
          if (card.description) {
            html += `      <p>${this.escapeHtml(card.description)}</p>\n`
          }
          html += `      <small>Priority: ${card.priority || "medium"}</small>\n`
          html += `    </div>\n`
        })

        html += `  </div>\n`
      })

      html += "</div>\n"
      return html
    } catch (error) {
      return "<p>Unable to render kanban board</p>"
    }
  }

  private kanbanToMarkdown(kanbanContent: string): string {
    try {
      const kanbanData = JSON.parse(kanbanContent)
      let markdown = ""

      kanbanData.columns?.forEach((column: any) => {
        markdown += `## ${column.title}\n\n`

        column.cards?.forEach((card: any) => {
          markdown += `### ${card.title}\n\n`
          if (card.description) {
            markdown += `${card.description}\n\n`
          }
          markdown += `**Priority:** ${card.priority || "medium"}\n\n`
        })
      })

      return markdown
    } catch (error) {
      return "Unable to render kanban board\n"
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement("div")
    div.textContent = text
    return div.innerHTML
  }

  downloadBlob(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

export const exportManager = new ExportManager()
