import type { User, Change } from "./types"

export interface CollaboratorPresence {
  userId: string
  user: User
  cursor?: { line: number; column: number }
  selection?: { start: number; end: number }
  lastSeen: Date
  color: string
}

export interface CollaborationEvent {
  type: "user-joined" | "user-left" | "cursor-moved" | "content-changed" | "selection-changed"
  userId: string
  noteId: string
  data?: any
  timestamp: Date
}

// Mock WebSocket for real-time collaboration
class MockWebSocket {
  private listeners: { [event: string]: Function[] } = {}
  private static instance: MockWebSocket

  static getInstance(): MockWebSocket {
    if (!MockWebSocket.instance) {
      MockWebSocket.instance = new MockWebSocket()
    }
    return MockWebSocket.instance
  }

  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(callback)
  }

  off(event: string, callback: Function) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter((cb) => cb !== callback)
    }
  }

  emit(event: string, data: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach((callback) => callback(data))
    }
  }

  // Simulate network delay
  private simulateDelay(callback: Function, data: any) {
    setTimeout(() => callback(data), Math.random() * 100 + 50)
  }

  send(data: CollaborationEvent) {
    // Simulate broadcasting to other users
    this.simulateDelay(() => {
      this.emit("collaboration-event", data)
    }, data)
  }
}

export class CollaborationManager {
  private socket: MockWebSocket
  private currentNoteId: string | null = null
  private currentUserId: string | null = null
  private collaborators: Map<string, CollaboratorPresence> = new Map()
  private eventListeners: { [event: string]: Function[] } = {}

  constructor() {
    this.socket = MockWebSocket.getInstance()
    this.setupSocketListeners()
  }

  private setupSocketListeners() {
    this.socket.on("collaboration-event", (event: CollaborationEvent) => {
      this.handleCollaborationEvent(event)
    })
  }

  private handleCollaborationEvent(event: CollaborationEvent) {
    // Don't process our own events
    if (event.userId === this.currentUserId) return

    switch (event.type) {
      case "user-joined":
        this.addCollaborator(event.data.user, event.userId)
        break
      case "user-left":
        this.removeCollaborator(event.userId)
        break
      case "cursor-moved":
        this.updateCollaboratorCursor(event.userId, event.data.cursor)
        break
      case "selection-changed":
        this.updateCollaboratorSelection(event.userId, event.data.selection)
        break
      case "content-changed":
        this.handleContentChange(event.data.change)
        break
    }

    this.emit("collaboration-update", {
      type: event.type,
      collaborators: Array.from(this.collaborators.values()),
    })
  }

  joinNote(noteId: string, user: User) {
    this.currentNoteId = noteId
    this.currentUserId = user.id

    // Add ourselves as a collaborator
    this.addCollaborator(user, user.id)

    // Notify others
    this.socket.send({
      type: "user-joined",
      userId: user.id,
      noteId,
      data: { user },
      timestamp: new Date(),
    })

    // Simulate some existing collaborators for demo
    this.simulateExistingCollaborators()
  }

  leaveNote() {
    if (this.currentNoteId && this.currentUserId) {
      this.socket.send({
        type: "user-left",
        userId: this.currentUserId,
        noteId: this.currentNoteId,
        timestamp: new Date(),
      })
    }

    this.collaborators.clear()
    this.currentNoteId = null
    this.currentUserId = null
  }

  updateCursor(cursor: { line: number; column: number }) {
    if (!this.currentNoteId || !this.currentUserId) return

    this.socket.send({
      type: "cursor-moved",
      userId: this.currentUserId,
      noteId: this.currentNoteId,
      data: { cursor },
      timestamp: new Date(),
    })
  }

  updateSelection(selection: { start: number; end: number }) {
    if (!this.currentNoteId || !this.currentUserId) return

    this.socket.send({
      type: "selection-changed",
      userId: this.currentUserId,
      noteId: this.currentNoteId,
      data: { selection },
      timestamp: new Date(),
    })
  }

  broadcastContentChange(change: Change) {
    if (!this.currentNoteId || !this.currentUserId) return

    this.socket.send({
      type: "content-changed",
      userId: this.currentUserId,
      noteId: this.currentNoteId,
      data: { change },
      timestamp: new Date(),
    })
  }

  private addCollaborator(user: User, userId: string) {
    const colors = ["#ef4444", "#f97316", "#eab308", "#22c55e", "#3b82f6", "#8b5cf6", "#ec4899"]
    const color = colors[Array.from(this.collaborators.keys()).length % colors.length]

    this.collaborators.set(userId, {
      userId,
      user,
      lastSeen: new Date(),
      color,
    })
  }

  private removeCollaborator(userId: string) {
    this.collaborators.delete(userId)
  }

  private updateCollaboratorCursor(userId: string, cursor: { line: number; column: number }) {
    const collaborator = this.collaborators.get(userId)
    if (collaborator) {
      this.collaborators.set(userId, {
        ...collaborator,
        cursor,
        lastSeen: new Date(),
      })
    }
  }

  private updateCollaboratorSelection(userId: string, selection: { start: number; end: number }) {
    const collaborator = this.collaborators.get(userId)
    if (collaborator) {
      this.collaborators.set(userId, {
        ...collaborator,
        selection,
        lastSeen: new Date(),
      })
    }
  }

  private handleContentChange(change: Change) {
    this.emit("content-change", change)
  }

  private simulateExistingCollaborators() {
    // Simulate 1-2 existing collaborators for demo purposes
    const demoUsers = [
      {
        id: "demo-user-1",
        name: "Alice Johnson",
        email: "<EMAIL>",
        avatar: "/diverse-user-avatars.png",
        createdAt: new Date(),
      },
      {
        id: "demo-user-2",
        name: "Bob Smith",
        email: "<EMAIL>",
        avatar: "/diverse-user-avatars.png",
        createdAt: new Date(),
      },
    ]

    // Randomly add 0-2 demo collaborators
    const numCollaborators = Math.floor(Math.random() * 3)
    for (let i = 0; i < numCollaborators; i++) {
      const user = demoUsers[i]
      setTimeout(
        () => {
          this.addCollaborator(user, user.id)
          this.emit("collaboration-update", {
            type: "user-joined",
            collaborators: Array.from(this.collaborators.values()),
          })
        },
        (i + 1) * 1000,
      )
    }
  }

  getCollaborators(): CollaboratorPresence[] {
    return Array.from(this.collaborators.values())
  }

  on(event: string, callback: Function) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = []
    }
    this.eventListeners[event].push(callback)
  }

  off(event: string, callback: Function) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter((cb) => cb !== callback)
    }
  }

  private emit(event: string, data: any) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach((callback) => callback(data))
    }
  }
}

export const collaborationManager = new CollaborationManager()
