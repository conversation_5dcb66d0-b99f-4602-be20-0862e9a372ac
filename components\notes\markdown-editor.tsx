"use client"

import { useState } from "react"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Eye, Edit, Split } from "lucide-react"
import type { Note } from "@/lib/types"

interface MarkdownEditorProps {
  note: Note
  onContentChange: (content: string) => void
}

export function MarkdownEditor({ note, onContentChange }: MarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<"edit" | "preview" | "split">("edit")

  // Simple markdown to HTML converter (basic implementation)
  const markdownToHtml = (markdown: string): string => {
    return markdown
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>")
      .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
      .replace(/\*(.*)\*/gim, "<em>$1</em>")
      .replace(/!\[([^\]]*)\]$$([^$$]*)\)/gim, '<img alt="$1" src="$2" />')
      .replace(/\[([^\]]*)\]$$([^$$]*)\)/gim, '<a href="$2">$1</a>')
      .replace(/\n$/gim, "<br />")
      .replace(/^> (.*$)/gim, "<blockquote>$1</blockquote>")
      .replace(/^\* (.*$)/gim, "<ul><li>$1</li></ul>")
      .replace(/^1\. (.*$)/gim, "<ol><li>$1</li></ol>")
      .replace(/`([^`]*)`/gim, "<code>$1</code>")
      .replace(/```([^```]*)```/gim, "<pre><code>$1</code></pre>")
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="editor-toolbar">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-fit grid-cols-3">
            <TabsTrigger value="edit" className="flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Edit
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="split" className="flex items-center gap-2">
              <Split className="w-4 h-4" />
              Split
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Editor Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} className="h-full">
          <TabsContent value="edit" className="h-full m-0">
            <Textarea
              value={note.content}
              onChange={(e) => onContentChange(e.target.value)}
              placeholder="Start writing in Markdown..."
              className="h-full resize-none border-0 rounded-none focus-visible:ring-0 font-mono"
            />
          </TabsContent>

          <TabsContent value="preview" className="h-full m-0">
            <div className="h-full p-4 overflow-auto">
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: markdownToHtml(note.content) }}
              />
            </div>
          </TabsContent>

          <TabsContent value="split" className="h-full m-0">
            <div className="grid grid-cols-2 h-full">
              <Textarea
                value={note.content}
                onChange={(e) => onContentChange(e.target.value)}
                placeholder="Start writing in Markdown..."
                className="h-full resize-none border-0 rounded-none focus-visible:ring-0 font-mono border-r"
              />
              <div className="h-full p-4 overflow-auto">
                <div
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: markdownToHtml(note.content) }}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
