"use client"

import { useState } from "react"
import { useAppStore } from "@/lib/store"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, FileText, Menu, Search, Download } from "lucide-react"
import { NoteSidebar } from "@/components/notes/note-sidebar"
import { NoteEditor } from "@/components/notes/note-editor"
import { AdvancedSearch } from "@/components/search/advanced-search"
import { ExportDialog } from "@/components/export/export-dialog"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import type { SearchResult } from "@/lib/types"

export function Dashboard() {
  const currentUser = useAppStore((state) => state.currentUser)
  const logout = useAppStore((state) => state.logout)
  const sidebarOpen = useAppStore((state) => state.sidebarOpen)
  const toggleSidebar = useAppStore((state) => state.toggleSidebar)
  const notes = useAppStore((state) => state.notes)
  const setActiveNote = useAppStore((state) => state.setActiveNote)

  const [showSearch, setShowSearch] = useState(false)
  const [showExport, setShowExport] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])

  const handleSignOut = async () => {
    await logout()
  }

  const handleSearchResults = (results: SearchResult[]) => {
    setSearchResults(results)
  }

  const handleSelectSearchResult = (result: SearchResult) => {
    const note = notes.find((n) => n.id === result.noteId)
    if (note) {
      setActiveNote(note)
      setShowSearch(false)
    }
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <header className="border-b border-border bg-card">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={toggleSidebar} className="lg:hidden">
              <Menu className="w-4 h-4" />
            </Button>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-bold">NoteTaker Pro</h1>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Dialog open={showSearch} onOpenChange={setShowSearch}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh]">
                <DialogHeader>
                  <DialogTitle>Search Notes</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <AdvancedSearch onResults={handleSearchResults} />

                  {searchResults.length > 0 && (
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      <h3 className="font-medium text-sm">Results ({searchResults.length})</h3>
                      {searchResults.map((result) => (
                        <div
                          key={result.noteId}
                          className="p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => handleSelectSearchResult(result)}
                        >
                          <h4 className="font-medium text-sm mb-1">{result.title}</h4>
                          <p className="text-xs text-muted-foreground line-clamp-2">{result.snippet}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-xs text-muted-foreground">
                              Relevance: {Math.round(result.relevance * 10) / 10}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>

            <Button variant="outline" size="sm" onClick={() => setShowExport(true)}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>

            <span className="text-sm text-muted-foreground hidden sm:block">Welcome, {currentUser?.name}</span>
            <Button variant="outline" size="sm" onClick={handleSignOut}>
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      <div className="flex-1 flex overflow-hidden">
        <div className={`${sidebarOpen ? "block" : "hidden"} lg:block`}>
          <NoteSidebar />
        </div>
        <NoteEditor />
      </div>

      <ExportDialog notes={notes} isOpen={showExport} onClose={() => setShowExport(false)} />
    </div>
  )
}
