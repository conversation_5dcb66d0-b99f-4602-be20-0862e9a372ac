import { WebSocketServer } from 'ws';
import { createServer } from 'http';

const port = 3001;
const server = createServer();
const wss = new WebSocketServer({ server });

console.log('Starting simple WebSocket server...');

wss.on('connection', (ws) => {
  console.log('New connection established');
  
  ws.on('message', (data) => {
    console.log('Received:', data.toString());
    ws.send(`Echo: ${data.toString()}`);
  });
  
  ws.on('close', () => {
    console.log('Connection closed');
  });
  
  ws.send('Welcome to WebSocket server!');
});

server.listen(port, () => {
  console.log(`WebSocket server running on port ${port}`);
});

process.on('SIGINT', () => {
  console.log('\nShutting down...');
  wss.close(() => {
    server.close(() => {
      process.exit(0);
    });
  });
});
