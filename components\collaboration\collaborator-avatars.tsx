"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import type { CollaboratorPresence } from "@/lib/collaboration"

interface CollaboratorAvatarsProps {
  collaborators: CollaboratorPresence[]
  maxVisible?: number
}

export function CollaboratorAvatars({ collaborators, maxVisible = 5 }: CollaboratorAvatarsProps) {
  const visibleCollaborators = collaborators.slice(0, maxVisible)
  const hiddenCount = Math.max(0, collaborators.length - maxVisible)

  if (collaborators.length === 0) {
    return null
  }

  return (
    <TooltipProvider>
      <div className="flex items-center gap-1">
        <div className="flex -space-x-2">
          {visibleCollaborators.map((collaborator) => (
            <Tooltip key={collaborator.userId}>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Avatar className="w-8 h-8 border-2 border-background">
                    <AvatarImage src={collaborator.user.avatar || "/placeholder.svg"} alt={collaborator.user.name} />
                    <AvatarFallback className="text-xs">
                      {collaborator.user.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div
                    className="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background"
                    style={{ backgroundColor: collaborator.color }}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="flex items-center gap-2">
                  <span>{collaborator.user.name}</span>
                  <Badge variant="secondary" className="collaboration-indicator">
                    Online
                  </Badge>
                </div>
              </TooltipContent>
            </Tooltip>
          ))}

          {hiddenCount > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                  <span className="text-xs font-medium">+{hiddenCount}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <span>
                  {hiddenCount} more collaborator{hiddenCount > 1 ? "s" : ""}
                </span>
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        <span className="text-xs text-muted-foreground ml-2">
          {collaborators.length} collaborator{collaborators.length !== 1 ? "s" : ""}
        </span>
      </div>
    </TooltipProvider>
  )
}
