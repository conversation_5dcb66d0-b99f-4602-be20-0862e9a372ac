"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Search, Filter, CalendarIcon, X, FileText, Hash, Kanban } from "lucide-react"
import { useAppStore } from "@/lib/store"
import { searchEngine, type SearchFilters, type SearchOptions } from "@/lib/search"
import { formatDate } from "@/lib/utils"
import type { SearchResult } from "@/lib/types"

interface AdvancedSearchProps {
  onResults: (results: SearchResult[]) => void
}

export function AdvancedSearch({ onResults }: AdvancedSearchProps) {
  const notes = useAppStore((state) => state.notes)
  const currentUser = useAppStore((state) => state.currentUser)

  const [query, setQuery] = useState("")
  const [filters, setFilters] = useState<SearchFilters>({
    type: "all",
    tags: [],
    sortBy: "relevance",
    sortOrder: "desc",
  })
  const [isSearching, setIsSearching] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>({})
  const [tagInput, setTagInput] = useState("")

  // Build search index when notes change
  useEffect(() => {
    searchEngine.buildIndex(notes)
  }, [notes])

  // Perform search when query or filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch()
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, filters, notes])

  const performSearch = async () => {
    setIsSearching(true)

    const searchOptions: SearchOptions = {
      query,
      filters: {
        ...filters,
        dateRange:
          dateRange.start && dateRange.end
            ? {
                start: dateRange.start,
                end: dateRange.end,
              }
            : undefined,
      },
      limit: 50,
    }

    try {
      const results = searchEngine.search(notes, searchOptions)
      onResults(results)
    } catch (error) {
      console.error("[v0] Search error:", error)
      onResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const addTag = () => {
    if (tagInput.trim() && !filters.tags?.includes(tagInput.trim())) {
      setFilters((prev) => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()],
      }))
      setTagInput("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFilters((prev) => ({
      ...prev,
      tags: prev.tags?.filter((tag) => tag !== tagToRemove) || [],
    }))
  }

  const clearFilters = () => {
    setFilters({
      type: "all",
      tags: [],
      sortBy: "relevance",
      sortOrder: "desc",
    })
    setDateRange({})
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "rich-text":
        return <FileText className="w-4 h-4" />
      case "markdown":
        return <Hash className="w-4 h-4" />
      case "kanban":
        return <Kanban className="w-4 h-4" />
      default:
        return <Search className="w-4 h-4" />
    }
  }

  const hasActiveFilters = () => {
    return (
      (filters.type && filters.type !== "all") ||
      (filters.tags && filters.tags.length > 0) ||
      dateRange.start ||
      dateRange.end ||
      filters.author
    )
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          placeholder="Search notes, content, and tags..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10 pr-12"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className={`absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 ${
            hasActiveFilters() ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <Filter className="w-4 h-4" />
        </Button>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Filters:</span>

          {filters.type && filters.type !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {getTypeIcon(filters.type)}
              <span className="capitalize">{filters.type.replace("-", " ")}</span>
              <X className="w-3 h-3 cursor-pointer" onClick={() => setFilters((prev) => ({ ...prev, type: "all" }))} />
            </Badge>
          )}

          {filters.tags?.map((tag) => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              #{tag}
              <X className="w-3 h-3 cursor-pointer" onClick={() => removeTag(tag)} />
            </Badge>
          ))}

          {(dateRange.start || dateRange.end) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <CalendarIcon className="w-3 h-3" />
              Date Range
              <X className="w-3 h-3 cursor-pointer" onClick={() => setDateRange({})} />
            </Badge>
          )}

          <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6 px-2 text-xs">
            Clear All
          </Button>
        </div>
      )}

      {/* Advanced Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Filter className="w-4 h-4" />
              Advanced Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Note Type Filter */}
              <div>
                <Label htmlFor="type-filter">Note Type</Label>
                <Select
                  value={filters.type || "all"}
                  onValueChange={(value: any) => setFilters((prev) => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="rich-text">Rich Text</SelectItem>
                    <SelectItem value="markdown">Markdown</SelectItem>
                    <SelectItem value="kanban">Kanban</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Options */}
              <div>
                <Label htmlFor="sort-filter">Sort By</Label>
                <Select
                  value={filters.sortBy || "relevance"}
                  onValueChange={(value: any) => setFilters((prev) => ({ ...prev, sortBy: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Relevance</SelectItem>
                    <SelectItem value="date">Date Modified</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="type">Type</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Tags Filter */}
            <div>
              <Label htmlFor="tags-filter">Tags</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag filter..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && addTag()}
                />
                <Button onClick={addTag} size="sm">
                  Add
                </Button>
              </div>
              {filters.tags && filters.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {filters.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="flex items-center gap-1">
                      #{tag}
                      <X className="w-3 h-3 cursor-pointer" onClick={() => removeTag(tag)} />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Date Range Filter */}
            <div>
              <Label>Date Range</Label>
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start text-left font-normal bg-transparent">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.start ? formatDate(dateRange.start) : "Start date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.start}
                      onSelect={(date) => setDateRange((prev) => ({ ...prev, start: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start text-left font-normal bg-transparent">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.end ? formatDate(dateRange.end) : "End date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.end}
                      onSelect={(date) => setDateRange((prev) => ({ ...prev, end: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Status */}
      {isSearching && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          Searching...
        </div>
      )}
    </div>
  )
}
