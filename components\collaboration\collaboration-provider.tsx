"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState, type <PERSON>actNode } from "react"
import { WebSocketClient } from "@/lib/websocket-client"
import { useAppStore } from "@/lib/store"

interface CollaborationUser {
  userId: number
  name: string
  email: string
  avatar_url?: string
  cursor_position: any
  last_seen: Date
  is_active: boolean
}

interface CollaborationContextType {
  wsClient: WebSocketClient | null
  collaborators: CollaborationUser[]
  isConnected: boolean
  connectToNote: (noteId: number) => Promise<void>
  disconnect: () => void
  sendCursorUpdate: (position: any) => void
  sendContentUpdate: (content: any) => void
}

const CollaborationContext = createContext<CollaborationContextType | null>(null)

export function CollaborationProvider({ children }: { children: ReactNode }) {
  const [wsClient] = useState(() => new WebSocketClient())
  const [collaborators, setCollaborators] = useState<CollaborationUser[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const currentUser = useAppStore((state) => state.currentUser)

  useEffect(() => {
    if (!wsClient) return

    wsClient.onUserListUpdate = (users) => {
      setCollaborators(users)
    }

    wsClient.onConnectionChange = (connected) => {
      setIsConnected(connected)
    }

    wsClient.onError = (error) => {
      console.error("WebSocket error:", error)
    }

    return () => {
      wsClient.disconnect()
    }
  }, [wsClient])

  const connectToNote = async (noteId: number) => {
    if (!currentUser || !wsClient) return

    const token = localStorage.getItem("auth_token")
    if (!token) return

    try {
      await wsClient.connect(noteId, token)
    } catch (error) {
      console.error("Failed to connect to collaboration:", error)
    }
  }

  const disconnect = () => {
    wsClient?.disconnect()
    setCollaborators([])
    setIsConnected(false)
  }

  const sendCursorUpdate = (position: any) => {
    wsClient?.sendCursorUpdate(position)
  }

  const sendContentUpdate = (content: any) => {
    wsClient?.sendContentUpdate(content)
  }

  return (
    <CollaborationContext.Provider
      value={{
        wsClient,
        collaborators,
        isConnected,
        connectToNote,
        disconnect,
        sendCursorUpdate,
        sendContentUpdate,
      }}
    >
      {children}
    </CollaborationContext.Provider>
  )
}

export function useCollaboration() {
  const context = useContext(CollaborationContext)
  if (!context) {
    throw new Error("useCollaboration must be used within CollaborationProvider")
  }
  return context
}
