import { NextResponse } from "next/server"
import { Database } from "@/lib/database"
import { withAuth, type AuthenticatedRequest } from "@/lib/middleware-auth"

// GET /api/collaboration/[noteId] - Get active collaborators for a note
export const GET = withAuth(async (request: AuthenticatedRequest, { params }: { params: { noteId: string } }) => {
  try {
    const noteId = Number.parseInt(params.noteId)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    // Check if user has access to the note
    const hasAccess = await checkNoteAccess(noteId, request.user.userId)
    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get active collaboration sessions (last 5 minutes)
    const collaborators = await Database.query(
      `SELECT cs.*, u.name, u.email, u.avatar_url 
       FROM collaboration_sessions cs 
       JOIN users u ON cs.user_id = u.id 
       WHERE cs.note_id = ? AND cs.last_seen > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
       ORDER BY cs.last_seen DESC`,
      [noteId],
    )

    const formattedCollaborators = collaborators.map((collab: any) => ({
      userId: collab.user_id,
      name: collab.name,
      email: collab.email,
      avatar_url: collab.avatar_url,
      cursor_position:
        typeof collab.cursor_position === "string" ? JSON.parse(collab.cursor_position) : collab.cursor_position,
      last_seen: collab.last_seen,
      is_active: new Date(collab.last_seen) > new Date(Date.now() - 2 * 60 * 1000), // Active in last 2 minutes
    }))

    return NextResponse.json({ collaborators: formattedCollaborators })
  } catch (error) {
    console.error("Get collaborators error:", error)
    return NextResponse.json({ error: "Failed to fetch collaborators" }, { status: 500 })
  }
})

async function checkNoteAccess(noteId: number, userId: number): Promise<boolean> {
  // Check if user owns the note
  const ownedNote = await Database.queryOne("SELECT id FROM notes WHERE id = ? AND owner_id = ?", [noteId, userId])

  if (ownedNote) return true

  // Check if note is shared with user
  const sharedNote = await Database.queryOne(
    "SELECT id FROM shared_notes WHERE note_id = ? AND (shared_with_email = (SELECT email FROM users WHERE id = ?) OR is_public = 1) AND (expires_at IS NULL OR expires_at > NOW())",
    [noteId, userId],
  )

  return !!sharedNote
}
