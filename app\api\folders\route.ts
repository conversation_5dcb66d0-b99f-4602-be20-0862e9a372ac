import { NextResponse } from "next/server"
import { z } from "zod"
import { Database } from "@/lib/database"
import { withAuth, type AuthenticatedRequest } from "@/lib/middleware-auth"

const createFolderSchema = z.object({
  name: z.string().min(1, "Folder name is required").max(255, "Name too long"),
  parent_id: z.number().optional(),
})

interface Folder {
  id: number
  name: string
  owner_id: number
  parent_id?: number
  created_at: Date
}

// GET /api/folders - Get user's folders
export const GET = withAuth(async (request: AuthenticatedRequest) => {
  try {
    const folders = await Database.query<Folder>("SELECT * FROM folders WHERE owner_id = ? ORDER BY name ASC", [
      request.user.userId,
    ])

    return NextResponse.json({ folders })
  } catch (error) {
    console.error("Get folders error:", error)
    return NextResponse.json({ error: "Failed to fetch folders" }, { status: 500 })
  }
})

// POST /api/folders - Create new folder
export const POST = with<PERSON>uth(async (request: AuthenticatedRequest) => {
  try {
    const body = await request.json()
    const { name, parent_id } = createFolderSchema.parse(body)

    // Check if parent folder exists and belongs to user
    if (parent_id) {
      const parentFolder = await Database.queryOne("SELECT * FROM folders WHERE id = ? AND owner_id = ?", [
        parent_id,
        request.user.userId,
      ])

      if (!parentFolder) {
        return NextResponse.json({ error: "Parent folder not found" }, { status: 404 })
      }
    }

    const folderId = await Database.insert("INSERT INTO folders (name, owner_id, parent_id) VALUES (?, ?, ?)", [
      name,
      request.user.userId,
      parent_id || null,
    ])

    const folder = await Database.queryOne<Folder>("SELECT * FROM folders WHERE id = ?", [folderId])

    return NextResponse.json({ folder, message: "Folder created successfully" }, { status: 201 })
  } catch (error) {
    console.error("Create folder error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create folder" }, { status: 500 })
  }
})
