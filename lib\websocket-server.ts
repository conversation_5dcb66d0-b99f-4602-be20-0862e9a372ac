import { WebSocketServer, WebSocket } from "ws"
import { createServer } from "http"
import { AuthUtils } from "./auth-utils.js"
import { Database } from "./database.js"

interface CollaborationMessage {
  type: "join" | "leave" | "cursor" | "content" | "user-list" | "ping"
  noteId?: number
  userId?: number
  data?: any
  timestamp?: number
}

interface ConnectedUser {
  ws: WebSocket
  userId: number
  userName: string
  noteId: number
  lastSeen: Date
}

class CollaborationServer {
  private wss: WebSocketServer
  private connections = new Map<WebSocket, ConnectedUser>()
  private noteRooms = new Map<number, Set<WebSocket>>()

  constructor(port = 3001) {
    const server = createServer()
    this.wss = new WebSocketServer({ server })

    this.wss.on("connection", this.handleConnection.bind(this))

    server.listen(port, () => {
      console.log(`WebSocket server running on port ${port}`)
    })

    // Cleanup inactive connections every 30 seconds
    setInterval(this.cleanupConnections.bind(this), 30000)
  }

  private async handleConnection(ws: WebSocket, request: any) {
    console.log("New WebSocket connection")

    ws.on("message", async (data) => {
      try {
        const message: CollaborationMessage = JSON.parse(data.toString())
        await this.handleMessage(ws, message)
      } catch (error) {
        console.error("WebSocket message error:", error)
        this.sendError(ws, "Invalid message format")
      }
    })

    ws.on("close", () => {
      this.handleDisconnection(ws)
    })

    ws.on("error", (error) => {
      console.error("WebSocket error:", error)
      this.handleDisconnection(ws)
    })

    // Send ping to keep connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: "ping", timestamp: Date.now() }))
      } else {
        clearInterval(pingInterval)
      }
    }, 30000)
  }

  private async handleMessage(ws: WebSocket, message: CollaborationMessage) {
    switch (message.type) {
      case "join":
        await this.handleJoin(ws, message)
        break
      case "leave":
        this.handleLeave(ws, message)
        break
      case "cursor":
        this.handleCursor(ws, message)
        break
      case "content":
        await this.handleContent(ws, message)
        break
      case "ping":
        // Respond to ping
        ws.send(JSON.stringify({ type: "pong", timestamp: Date.now() }))
        break
      default:
        this.sendError(ws, "Unknown message type")
    }
  }

  private async handleJoin(ws: WebSocket, message: CollaborationMessage) {
    const { noteId, data } = message
    if (!noteId || !data?.token) {
      return this.sendError(ws, "Missing noteId or token")
    }

    // Verify JWT token
    const payload = AuthUtils.verifyToken(data.token)
    if (!payload) {
      return this.sendError(ws, "Invalid token")
    }

    // Check if user has access to the note
    const hasAccess = await this.checkNoteAccess(noteId, payload.userId)
    if (!hasAccess) {
      return this.sendError(ws, "Access denied")
    }

    // Store connection info
    const user: ConnectedUser = {
      ws,
      userId: payload.userId,
      userName: payload.name,
      noteId,
      lastSeen: new Date(),
    }

    this.connections.set(ws, user)

    // Add to note room
    if (!this.noteRooms.has(noteId)) {
      this.noteRooms.set(noteId, new Set())
    }
    this.noteRooms.get(noteId)!.add(ws)

    // Update collaboration session in database
    await Database.query(
      `INSERT INTO collaboration_sessions (note_id, user_id, cursor_position, last_seen) 
       VALUES (?, ?, ?, ?) 
       ON DUPLICATE KEY UPDATE last_seen = VALUES(last_seen)`,
      [noteId, payload.userId, JSON.stringify({}), new Date()],
    )

    // Send current user list to the new user
    this.sendUserList(noteId)

    // Notify others about new user
    this.broadcastToNote(
      noteId,
      {
        type: "user-list",
        data: await this.getNoteUsers(noteId),
      },
      ws,
    )

    console.log(`User ${payload.name} joined note ${noteId}`)
  }

  private handleLeave(ws: WebSocket, message: CollaborationMessage) {
    this.handleDisconnection(ws)
  }

  private async handleCursor(ws: WebSocket, message: CollaborationMessage) {
    const user = this.connections.get(ws)
    if (!user) return

    // Update cursor position in database
    await Database.query(
      "UPDATE collaboration_sessions SET cursor_position = ?, last_seen = ? WHERE note_id = ? AND user_id = ?",
      [JSON.stringify(message.data), new Date(), user.noteId, user.userId],
    )

    // Broadcast cursor position to other users in the same note
    this.broadcastToNote(
      user.noteId,
      {
        type: "cursor",
        userId: user.userId,
        data: message.data,
        timestamp: Date.now(),
      },
      ws,
    )
  }

  private async handleContent(ws: WebSocket, message: CollaborationMessage) {
    const user = this.connections.get(ws)
    if (!user) return

    // Update note content in database
    if (message.data?.content !== undefined) {
      await Database.query("UPDATE notes SET content = ?, updated_at = ? WHERE id = ? AND owner_id = ?", [
        message.data.content,
        new Date(),
        user.noteId,
        user.userId,
      ])

      // Log activity
      await Database.insert("INSERT INTO activity_log (note_id, user_id, action, details) VALUES (?, ?, ?, ?)", [
        user.noteId,
        user.userId,
        "updated",
        JSON.stringify({ real_time: true }),
      ])
    }

    // Broadcast content changes to other users
    this.broadcastToNote(
      user.noteId,
      {
        type: "content",
        userId: user.userId,
        data: message.data,
        timestamp: Date.now(),
      },
      ws,
    )
  }

  private handleDisconnection(ws: WebSocket) {
    const user = this.connections.get(ws)
    if (!user) return

    // Remove from connections
    this.connections.delete(ws)

    // Remove from note room
    const noteRoom = this.noteRooms.get(user.noteId)
    if (noteRoom) {
      noteRoom.delete(ws)
      if (noteRoom.size === 0) {
        this.noteRooms.delete(user.noteId)
      }
    }

    // Update last seen in database
    Database.query("UPDATE collaboration_sessions SET last_seen = ? WHERE note_id = ? AND user_id = ?", [
      new Date(),
      user.noteId,
      user.userId,
    ]).catch(console.error)

    // Notify others about user leaving
    this.sendUserList(user.noteId)

    console.log(`User ${user.userName} left note ${user.noteId}`)
  }

  private async checkNoteAccess(noteId: number, userId: number): Promise<boolean> {
    // Check if user owns the note
    const ownedNote = await Database.queryOne("SELECT id FROM notes WHERE id = ? AND owner_id = ?", [noteId, userId])

    if (ownedNote) return true

    // Check if note is shared with user
    const sharedNote = await Database.queryOne(
      "SELECT id FROM shared_notes WHERE note_id = ? AND (shared_with_email = (SELECT email FROM users WHERE id = ?) OR is_public = 1) AND (expires_at IS NULL OR expires_at > NOW())",
      [noteId, userId],
    )

    return !!sharedNote
  }

  private async getNoteUsers(noteId: number): Promise<any[]> {
    const sessions = await Database.query(
      `SELECT cs.*, u.name, u.email, u.avatar_url 
       FROM collaboration_sessions cs 
       JOIN users u ON cs.user_id = u.id 
       WHERE cs.note_id = ? AND cs.last_seen > DATE_SUB(NOW(), INTERVAL 5 MINUTE)`,
      [noteId],
    )

    return sessions.map((session: any) => ({
      userId: session.user_id,
      name: session.name,
      email: session.email,
      avatar_url: session.avatar_url,
      cursor_position:
        typeof session.cursor_position === "string" ? JSON.parse(session.cursor_position) : session.cursor_position,
      last_seen: session.last_seen,
    }))
  }

  private async sendUserList(noteId: number) {
    const users = await this.getNoteUsers(noteId)
    this.broadcastToNote(noteId, {
      type: "user-list",
      data: users,
    })
  }

  private broadcastToNote(noteId: number, message: any, exclude?: WebSocket) {
    const noteRoom = this.noteRooms.get(noteId)
    if (!noteRoom) return

    const messageStr = JSON.stringify(message)
    noteRoom.forEach((ws) => {
      if (ws !== exclude && ws.readyState === WebSocket.OPEN) {
        ws.send(messageStr)
      }
    })
  }

  private sendError(ws: WebSocket, error: string) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: "error", error }))
    }
  }

  private cleanupConnections() {
    const now = new Date()
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000)

    this.connections.forEach((user, ws) => {
      if (user.lastSeen < fiveMinutesAgo || ws.readyState !== WebSocket.OPEN) {
        this.handleDisconnection(ws)
      }
    })
  }
}

// Start WebSocket server
let collaborationServer: CollaborationServer | null = null

export function startWebSocketServer(port?: number) {
  if (!collaborationServer) {
    collaborationServer = new CollaborationServer(port)
  }
  return collaborationServer
}

export { CollaborationServer }
