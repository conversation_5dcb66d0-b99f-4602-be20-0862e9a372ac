import bcrypt from "bcryptjs"
import jwt from "jsonwebtoken"
import { Database, type User } from "./database.js"

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-in-production"
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d"

export interface JWTPayload {
  userId: number
  email: string
  name: string
}

export class AuthUtils {
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  static generateToken(payload: JWTPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
  }

  static verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as JWTPayload
    } catch {
      return null
    }
  }

  static async createUser(email: string, password: string, name: string): Promise<User> {
    const passwordHash = await this.hashPassword(password)

    const userId = await Database.insert("INSERT INTO users (email, password_hash, name) VALUES (?, ?, ?)", [
      email,
      passwordHash,
      name,
    ])

    const user = await Database.queryOne<User>("SELECT * FROM users WHERE id = ?", [userId])

    if (!user) {
      throw new Error("Failed to create user")
    }

    return user
  }

  static async authenticateUser(email: string, password: string): Promise<User | null> {
    const user = await Database.queryOne<User>("SELECT * FROM users WHERE email = ?", [email])

    if (!user) {
      return null
    }

    const isValid = await this.verifyPassword(password, user.password_hash)
    return isValid ? user : null
  }

  static async getUserById(id: number): Promise<User | null> {
    return Database.queryOne<User>("SELECT * FROM users WHERE id = ?", [id])
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    return Database.queryOne<User>("SELECT * FROM users WHERE email = ?", [email])
  }
}
