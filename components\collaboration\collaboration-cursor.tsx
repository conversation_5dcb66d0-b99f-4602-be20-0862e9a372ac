"use client"

import type React from "react"

import { useEffect, useState } from "react"
import type { CollaboratorPresence } from "@/lib/collaboration"

interface CollaborationCursorProps {
  collaborator: CollaboratorPresence
  editorRef: React.RefObject<HTMLElement>
}

export function CollaborationCursor({ collaborator, editorRef }: CollaborationCursorProps) {
  const [position, setPosition] = useState<{ x: number; y: number } | null>(null)

  useEffect(() => {
    if (!collaborator.cursor || !editorRef.current) {
      setPosition(null)
      return
    }

    // Calculate cursor position based on line and column
    // This is a simplified implementation - in a real app you'd use the editor's API
    const editorRect = editorRef.current.getBoundingClientRect()
    const lineHeight = 24 // Approximate line height
    const charWidth = 8 // Approximate character width

    const x = editorRect.left + collaborator.cursor.column * charWidth
    const y = editorRect.top + collaborator.cursor.line * lineHeight

    setPosition({ x, y })
  }, [collaborator.cursor, editorRef])

  if (!position) return null

  return (
    <div
      className="fixed pointer-events-none z-50 transition-all duration-100"
      style={{
        left: position.x,
        top: position.y,
      }}
    >
      {/* Cursor line */}
      <div className="w-0.5 h-6 animate-pulse" style={{ backgroundColor: collaborator.color }} />

      {/* User label */}
      <div
        className="absolute top-6 left-0 px-2 py-1 rounded text-xs text-white whitespace-nowrap shadow-lg"
        style={{ backgroundColor: collaborator.color }}
      >
        {collaborator.user.name}
      </div>
    </div>
  )
}
