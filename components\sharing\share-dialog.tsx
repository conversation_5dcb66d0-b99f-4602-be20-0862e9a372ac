"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Copy, Mail, Link, Globe, Eye, Edit, Shield, Trash2, Clock, Users } from "lucide-react"
import { useAppStore } from "@/lib/store"
import { sharingManager, type ShareLink, type ShareInvitation } from "@/lib/sharing"
import { validateEmail } from "@/lib/auth"
import { formatDate } from "@/lib/utils"
import type { Note } from "@/lib/types"

interface ShareDialogProps {
  note: Note
  isOpen: boolean
  onClose: () => void
}

export function ShareDialog({ note, isOpen, onClose }: ShareDialogProps) {
  const updateNote = useAppStore((state) => state.updateNote)
  const currentUser = useAppStore((state) => state.currentUser)

  const [activeTab, setActiveTab] = useState("link")
  const [inviteEmail, setInviteEmail] = useState("")
  const [invitePermission, setInvitePermission] = useState<"view" | "edit" | "admin">("view")
  const [shareLinks, setShareLinks] = useState<ShareLink[]>([])
  const [invitations, setInvitations] = useState<ShareInvitation[]>([])
  const [isPublic, setIsPublic] = useState(note.shareSettings.isPublic)
  const [linkPermission, setLinkPermission] = useState<"view" | "edit">("view")
  const [linkExpiry, setLinkExpiry] = useState<string>("never")

  useEffect(() => {
    if (isOpen) {
      loadShareData()
    }
  }, [isOpen, note.id])

  const loadShareData = () => {
    setShareLinks(sharingManager.getShareLinksForNote(note.id))
    setInvitations(sharingManager.getInvitationsForNote(note.id))
    setIsPublic(note.shareSettings.isPublic)
  }

  const handleCreateShareLink = () => {
    if (!currentUser) return

    const expiresIn =
      linkExpiry === "never"
        ? undefined
        : linkExpiry === "1day"
          ? 24 * 60 * 60 * 1000
          : linkExpiry === "7days"
            ? 7 * 24 * 60 * 60 * 1000
            : linkExpiry === "30days"
              ? 30 * 24 * 60 * 60 * 1000
              : undefined

    const shareLink = sharingManager.generateShareLink(note.id, linkPermission, currentUser.id, expiresIn)
    setShareLinks([...shareLinks, shareLink])
  }

  const handleCopyLink = (token: string) => {
    const url = sharingManager.getPublicShareUrl(note.id, token)
    navigator.clipboard.writeText(url)
    // In a real app, you'd show a toast notification
    console.log("[v0] Link copied to clipboard:", url)
  }

  const handleRevokeLink = (linkId: string) => {
    sharingManager.revokeShareLink(linkId)
    setShareLinks(shareLinks.filter((link) => link.id !== linkId))
  }

  const handleInviteUser = () => {
    if (!currentUser || !validateEmail(inviteEmail)) return

    const invitation = sharingManager.inviteUserByEmail(note.id, inviteEmail, invitePermission, currentUser.id)
    setInvitations([...invitations, invitation])
    setInviteEmail("")
  }

  const handleTogglePublic = (checked: boolean) => {
    setIsPublic(checked)
    updateNote(note.id, {
      shareSettings: {
        ...note.shareSettings,
        isPublic: checked,
      },
    })
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "view":
        return <Eye className="w-4 h-4" />
      case "edit":
        return <Edit className="w-4 h-4" />
      case "admin":
        return <Shield className="w-4 h-4" />
      default:
        return <Eye className="w-4 h-4" />
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "view":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "edit":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "admin":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "accepted":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "declined":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Share "{note.title}"
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Public Access Toggle */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <CardTitle className="text-sm">Public Access</CardTitle>
                </div>
                <Switch checked={isPublic} onCheckedChange={handleTogglePublic} />
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {isPublic ? "Anyone with the link can view this note" : "Only people you invite can access this note"}
              </p>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="link" className="flex items-center gap-2">
                <Link className="w-4 h-4" />
                Share Link
              </TabsTrigger>
              <TabsTrigger value="invite" className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Invite People
              </TabsTrigger>
            </TabsList>

            <TabsContent value="link" className="space-y-4">
              {/* Create New Link */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Create Share Link</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="link-permission">Permission</Label>
                      <Select value={linkPermission} onValueChange={(value: any) => setLinkPermission(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="view">View only</SelectItem>
                          <SelectItem value="edit">Can edit</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="link-expiry">Expires</Label>
                      <Select value={linkExpiry} onValueChange={setLinkExpiry}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="never">Never</SelectItem>
                          <SelectItem value="1day">1 day</SelectItem>
                          <SelectItem value="7days">7 days</SelectItem>
                          <SelectItem value="30days">30 days</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button onClick={handleCreateShareLink} className="w-full">
                    <Link className="w-4 h-4 mr-2" />
                    Create Link
                  </Button>
                </CardContent>
              </Card>

              {/* Existing Links */}
              {shareLinks.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Active Links ({shareLinks.length})</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {shareLinks.map((link) => (
                      <div key={link.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant="secondary" className={getPermissionColor(link.permissions)}>
                              {getPermissionIcon(link.permissions)}
                              <span className="ml-1 capitalize">{link.permissions}</span>
                            </Badge>
                            {link.expiresAt && (
                              <Badge variant="outline" className="text-xs">
                                <Clock className="w-3 h-3 mr-1" />
                                Expires {formatDate(link.expiresAt)}
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Created {formatDate(link.createdAt)} • {link.accessCount} access
                            {link.accessCount !== 1 ? "es" : ""}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleCopyLink(link.token)}>
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleRevokeLink(link.id)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="invite" className="space-y-4">
              {/* Invite Form */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Invite by Email</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="invite-email">Email Address</Label>
                    <Input
                      id="invite-email"
                      type="email"
                      placeholder="Enter email address"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="invite-permission">Permission</Label>
                    <Select value={invitePermission} onValueChange={(value: any) => setInvitePermission(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view">View only</SelectItem>
                        <SelectItem value="edit">Can edit</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={handleInviteUser} className="w-full" disabled={!validateEmail(inviteEmail)}>
                    <Mail className="w-4 h-4 mr-2" />
                    Send Invitation
                  </Button>
                </CardContent>
              </Card>

              {/* Pending Invitations */}
              {invitations.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Invitations ({invitations.length})</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {invitations.map((invitation) => (
                      <div key={invitation.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs">
                              {invitation.invitedEmail.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">{invitation.invitedEmail}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="secondary" className={getPermissionColor(invitation.permissions)}>
                                {getPermissionIcon(invitation.permissions)}
                                <span className="ml-1 capitalize">{invitation.permissions}</span>
                              </Badge>
                              <Badge variant="outline" className={getStatusColor(invitation.status)}>
                                {invitation.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
