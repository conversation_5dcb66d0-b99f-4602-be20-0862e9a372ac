"use client"

import { useEffect } from "react"
import { useAppStore } from "@/lib/store"
import { AuthPage } from "@/components/auth/auth-page"
import { Dashboard } from "@/components/dashboard/dashboard"

export default function Home() {
  const currentUser = useAppStore((state) => state.currentUser)
  const loadCurrentUser = useAppStore((state) => state.loadCurrentUser)
  const loadNotes = useAppStore((state) => state.loadNotes)

  useEffect(() => {
    const initializeApp = async () => {
      // Try to load current user from token
      await loadCurrentUser()
    }

    initializeApp()
  }, [loadCurrentUser])

  useEffect(() => {
    if (currentUser) {
      loadNotes()
    }
  }, [currentUser, loadNotes])

  if (!currentUser) {
    return <AuthPage />
  }

  return <Dashboard />
}
