"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Users, Activity } from "lucide-react"
import { collaborationManager, type CollaboratorPresence } from "@/lib/collaboration"
import { formatDate } from "@/lib/utils"

interface CollaborationPanelProps {
  isOpen: boolean
  onClose: () => void
}

export function CollaborationPanel({ isOpen, onClose }: CollaborationPanelProps) {
  const [collaborators, setCollaborators] = useState<CollaboratorPresence[]>([])
  const [recentActivity, setRecentActivity] = useState<any[]>([])

  useEffect(() => {
    const handleCollaborationUpdate = (data: any) => {
      setCollaborators(data.collaborators || [])

      // Add to recent activity
      if (data.type !== "cursor-moved" && data.type !== "selection-changed") {
        setRecentActivity((prev) => [
          {
            id: Date.now(),
            type: data.type,
            timestamp: new Date(),
            user: data.collaborators?.find((c: any) => c.userId === data.userId)?.user,
          },
          ...prev.slice(0, 9), // Keep last 10 activities
        ])
      }
    }

    collaborationManager.on("collaboration-update", handleCollaborationUpdate)

    return () => {
      collaborationManager.off("collaboration-update", handleCollaborationUpdate)
    }
  }, [])

  if (!isOpen) return null

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "user-joined":
        return <Users className="w-4 h-4 text-green-500" />
      case "user-left":
        return <Users className="w-4 h-4 text-red-500" />
      case "content-changed":
        return <Activity className="w-4 h-4 text-blue-500" />
      default:
        return <Activity className="w-4 h-4 text-muted-foreground" />
    }
  }

  const getActivityMessage = (activity: any) => {
    switch (activity.type) {
      case "user-joined":
        return `${activity.user?.name || "Someone"} joined the document`
      case "user-left":
        return `${activity.user?.name || "Someone"} left the document`
      case "content-changed":
        return `${activity.user?.name || "Someone"} made changes`
      default:
        return "Unknown activity"
    }
  }

  return (
    <div className="fixed inset-y-0 right-0 w-80 bg-background border-l border-border shadow-lg z-40">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold flex items-center gap-2">
              <Users className="w-5 h-5" />
              Collaboration
            </h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-4 space-y-6">
            {/* Active Collaborators */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Active Collaborators ({collaborators.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.userId} className="flex items-center gap-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8">
                        <AvatarImage
                          src={collaborator.user.avatar || "/placeholder.svg"}
                          alt={collaborator.user.name}
                        />
                        <AvatarFallback className="text-xs">
                          {collaborator.user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div
                        className="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background"
                        style={{ backgroundColor: collaborator.color }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{collaborator.user.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Active {Math.floor((Date.now() - collaborator.lastSeen.getTime()) / 1000)}s ago
                      </p>
                    </div>
                    <Badge variant="secondary" className="collaboration-indicator">
                      Online
                    </Badge>
                  </div>
                ))}

                {collaborators.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">No other collaborators online</p>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm">{getActivityMessage(activity)}</p>
                      <p className="text-xs text-muted-foreground">{formatDate(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}

                {recentActivity.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">No recent activity</p>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
