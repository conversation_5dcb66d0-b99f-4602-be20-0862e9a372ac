import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateShareLink(noteId: string): string {
  return `${window.location.origin}/shared/${noteId}`
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date)
}

export function searchNotes(notes: any[], query: string) {
  if (!query.trim()) return notes

  const lowercaseQuery = query.toLowerCase()
  return notes.filter(
    (note) =>
      note.title.toLowerCase().includes(lowercaseQuery) ||
      note.content.toLowerCase().includes(lowercaseQuery) ||
      note.tags.some((tag: string) => tag.toLowerCase().includes(lowercaseQuery)),
  )
}

export function exportNote(note: any, format: "pdf" | "docx" | "html" | "markdown") {
  // Placeholder for export functionality
  console.log(`Exporting note ${note.id} as ${format}`)
}

export function generateId(): string {
  return crypto.randomUUID()
}

export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
