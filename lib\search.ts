import type { Note, SearchResult } from "./types"

export interface SearchFilters {
  type?: "rich-text" | "markdown" | "kanban" | "all"
  tags?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  author?: string
  sortBy?: "relevance" | "date" | "title" | "type"
  sortOrder?: "asc" | "desc"
}

export interface SearchOptions {
  query: string
  filters?: SearchFilters
  limit?: number
  offset?: number
}

export class SearchEngine {
  private searchIndex: Map<string, Set<string>> = new Map()

  // Build search index for faster searching
  buildIndex(notes: Note[]) {
    this.searchIndex.clear()

    notes.forEach((note) => {
      const words = this.extractWords(note)
      words.forEach((word) => {
        if (!this.searchIndex.has(word)) {
          this.searchIndex.set(word, new Set())
        }
        this.searchIndex.get(word)!.add(note.id)
      })
    })
  }

  private extractWords(note: Note): string[] {
    const text = `${note.title} ${note.content} ${note.tags.join(" ")}`
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter((word) => word.length > 2)
  }

  search(notes: Note[], options: SearchOptions): SearchResult[] {
    const { query, filters = {}, limit = 50, offset = 0 } = options

    if (!query.trim() && !this.hasActiveFilters(filters)) {
      return this.applyFiltersAndSort(notes, filters)
        .slice(offset, offset + limit)
        .map((note) => ({
          noteId: note.id,
          title: note.title,
          snippet: this.generateSnippet(note, ""),
          relevance: 1,
        }))
    }

    // Get candidate notes from search index
    const candidateNoteIds = this.getCandidateNotes(query)
    const candidateNotes = notes.filter((note) => candidateNoteIds.has(note.id))

    // Calculate relevance scores
    const results = candidateNotes.map((note) => {
      const relevance = this.calculateRelevance(note, query)
      return {
        noteId: note.id,
        title: note.title,
        snippet: this.generateSnippet(note, query),
        relevance,
      }
    })

    // Apply filters
    const filteredResults = this.applyFilters(results, notes, filters)

    // Sort results
    const sortedResults = this.sortResults(filteredResults, filters.sortBy, filters.sortOrder)

    return sortedResults.slice(offset, offset + limit)
  }

  private hasActiveFilters(filters: SearchFilters): boolean {
    return !!(
      (filters.type && filters.type !== "all") ||
      (filters.tags && filters.tags.length > 0) ||
      filters.dateRange ||
      filters.author
    )
  }

  private getCandidateNotes(query: string): Set<string> {
    const queryWords = query
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 2)
    const candidateNoteIds = new Set<string>()

    queryWords.forEach((word) => {
      // Exact match
      if (this.searchIndex.has(word)) {
        this.searchIndex.get(word)!.forEach((noteId) => candidateNoteIds.add(noteId))
      }

      // Partial match (prefix)
      for (const [indexWord, noteIds] of this.searchIndex.entries()) {
        if (indexWord.startsWith(word) || indexWord.includes(word)) {
          noteIds.forEach((noteId) => candidateNoteIds.add(noteId))
        }
      }
    })

    return candidateNoteIds
  }

  private calculateRelevance(note: Note, query: string): number {
    const queryLower = query.toLowerCase()
    let score = 0

    // Title match (highest weight)
    if (note.title.toLowerCase().includes(queryLower)) {
      score += 10
      if (note.title.toLowerCase().startsWith(queryLower)) {
        score += 5
      }
    }

    // Content match
    const contentMatches = (note.content.toLowerCase().match(new RegExp(queryLower, "g")) || []).length
    score += contentMatches * 2

    // Tag match
    const tagMatches = note.tags.filter((tag) => tag.toLowerCase().includes(queryLower)).length
    score += tagMatches * 3

    // Recency boost
    const daysSinceUpdate = (Date.now() - note.updatedAt.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate < 7) {
      score += 2
    } else if (daysSinceUpdate < 30) {
      score += 1
    }

    return score
  }

  private generateSnippet(note: Note, query: string): string {
    const maxLength = 150
    let content = note.content

    // For kanban notes, extract text from cards
    if (note.type === "kanban") {
      try {
        const kanbanData = JSON.parse(note.content)
        const cardTexts =
          kanbanData.columns?.flatMap(
            (col: any) => col.cards?.map((card: any) => `${card.title} ${card.description || ""}`) || [],
          ) || []
        content = cardTexts.join(" ")
      } catch (error) {
        content = "Kanban board"
      }
    }

    if (!query.trim()) {
      return content.length > maxLength ? content.substring(0, maxLength) + "..." : content
    }

    // Find the best snippet around the query
    const queryLower = query.toLowerCase()
    const contentLower = content.toLowerCase()
    const queryIndex = contentLower.indexOf(queryLower)

    if (queryIndex === -1) {
      return content.length > maxLength ? content.substring(0, maxLength) + "..." : content
    }

    const start = Math.max(0, queryIndex - 50)
    const end = Math.min(content.length, queryIndex + query.length + 50)
    let snippet = content.substring(start, end)

    if (start > 0) snippet = "..." + snippet
    if (end < content.length) snippet = snippet + "..."

    return snippet
  }

  private applyFilters(results: SearchResult[], notes: Note[], filters: SearchFilters): SearchResult[] {
    return results.filter((result) => {
      const note = notes.find((n) => n.id === result.noteId)
      if (!note) return false

      // Type filter
      if (filters.type && filters.type !== "all" && note.type !== filters.type) {
        return false
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some((tag) =>
          note.tags.some((noteTag) => noteTag.toLowerCase().includes(tag.toLowerCase())),
        )
        if (!hasMatchingTag) return false
      }

      // Date range filter
      if (filters.dateRange) {
        const noteDate = new Date(note.updatedAt)
        if (noteDate < filters.dateRange.start || noteDate > filters.dateRange.end) {
          return false
        }
      }

      // Author filter
      if (filters.author && note.authorId !== filters.author) {
        return false
      }

      return true
    })
  }

  private applyFiltersAndSort(notes: Note[], filters: SearchFilters): Note[] {
    let filteredNotes = [...notes]

    // Apply filters
    if (filters.type && filters.type !== "all") {
      filteredNotes = filteredNotes.filter((note) => note.type === filters.type)
    }

    if (filters.tags && filters.tags.length > 0) {
      filteredNotes = filteredNotes.filter((note) =>
        filters.tags!.some((tag) => note.tags.some((noteTag) => noteTag.toLowerCase().includes(tag.toLowerCase()))),
      )
    }

    if (filters.dateRange) {
      filteredNotes = filteredNotes.filter((note) => {
        const noteDate = new Date(note.updatedAt)
        return noteDate >= filters.dateRange!.start && noteDate <= filters.dateRange!.end
      })
    }

    if (filters.author) {
      filteredNotes = filteredNotes.filter((note) => note.authorId === filters.author)
    }

    return filteredNotes
  }

  private sortResults(results: SearchResult[], sortBy = "relevance", sortOrder = "desc"): SearchResult[] {
    return results.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case "relevance":
          comparison = a.relevance - b.relevance
          break
        case "title":
          comparison = a.title.localeCompare(b.title)
          break
        default:
          comparison = a.relevance - b.relevance
      }

      return sortOrder === "desc" ? -comparison : comparison
    })
  }
}

export const searchEngine = new SearchEngine()
