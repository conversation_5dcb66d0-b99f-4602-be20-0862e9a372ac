import { WebSocketServer } from 'ws';
import { createServer } from 'http';

const WS_PORT = Number(process.env.WS_PORT) || 3001;

console.log("Starting WebSocket server...");

// Create HTTP server
const server = createServer();

// Create WebSocket server
const wss = new WebSocketServer({ server });

// Store connections and rooms
const connections = new Map();
const noteRooms = new Map();

wss.on('connection', (ws, request) => {
  console.log('New WebSocket connection established');

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('Received message:', message.type);
      
      // Handle different message types
      switch (message.type) {
        case 'join':
          handleJoin(ws, message);
          break;
        case 'leave':
          handleLeave(ws, message);
          break;
        case 'cursor':
          handleCursor(ws, message);
          break;
        case 'content':
          handleContent(ws, message);
          break;
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
          break;
        default:
          ws.send(JSON.stringify({ type: 'error', error: 'Unknown message type' }));
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
      ws.send(JSON.stringify({ type: 'error', error: 'Invalid message format' }));
    }
  });

  ws.on('close', () => {
    console.log('WebSocket connection closed');
    handleDisconnection(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    handleDisconnection(ws);
  });

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to WebSocket server',
    timestamp: Date.now()
  }));
});

function handleJoin(ws, message) {
  const { noteId, data } = message;
  if (!noteId) {
    return sendError(ws, 'Missing noteId');
  }

  // Store connection info (simplified - no auth for now)
  const user = {
    ws,
    userId: data?.userId || Math.floor(Math.random() * 1000),
    userName: data?.userName || 'Anonymous',
    noteId,
    lastSeen: new Date(),
  };

  connections.set(ws, user);

  // Add to note room
  if (!noteRooms.has(noteId)) {
    noteRooms.set(noteId, new Set());
  }
  noteRooms.get(noteId).add(ws);

  // Notify others about new user
  broadcastToNote(noteId, {
    type: 'user-joined',
    userId: user.userId,
    userName: user.userName,
    timestamp: Date.now()
  }, ws);

  console.log(`User ${user.userName} joined note ${noteId}`);
}

function handleLeave(ws, message) {
  handleDisconnection(ws);
}

function handleCursor(ws, message) {
  const user = connections.get(ws);
  if (!user) return;

  // Broadcast cursor position to other users in the same note
  broadcastToNote(user.noteId, {
    type: 'cursor',
    userId: user.userId,
    data: message.data,
    timestamp: Date.now(),
  }, ws);
}

function handleContent(ws, message) {
  const user = connections.get(ws);
  if (!user) return;

  // Broadcast content changes to other users
  broadcastToNote(user.noteId, {
    type: 'content',
    userId: user.userId,
    data: message.data,
    timestamp: Date.now(),
  }, ws);
}

function handleDisconnection(ws) {
  const user = connections.get(ws);
  if (!user) return;

  // Remove from connections
  connections.delete(ws);

  // Remove from note room
  const noteRoom = noteRooms.get(user.noteId);
  if (noteRoom) {
    noteRoom.delete(ws);
    if (noteRoom.size === 0) {
      noteRooms.delete(user.noteId);
    }
  }

  // Notify others about user leaving
  broadcastToNote(user.noteId, {
    type: 'user-left',
    userId: user.userId,
    userName: user.userName,
    timestamp: Date.now()
  });

  console.log(`User ${user.userName} left note ${user.noteId}`);
}

function broadcastToNote(noteId, message, exclude) {
  const noteRoom = noteRooms.get(noteId);
  if (!noteRoom) return;

  const messageStr = JSON.stringify(message);
  noteRoom.forEach((ws) => {
    if (ws !== exclude && ws.readyState === 1) { // WebSocket.OPEN = 1
      ws.send(messageStr);
    }
  });
}

function sendError(ws, error) {
  if (ws.readyState === 1) { // WebSocket.OPEN = 1
    ws.send(JSON.stringify({ type: 'error', error }));
  }
}

// Start the server
server.listen(WS_PORT, () => {
  console.log(`WebSocket server started on port ${WS_PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down WebSocket server...');
  wss.close(() => {
    server.close(() => {
      console.log('WebSocket server stopped');
      process.exit(0);
    });
  });
});

// Cleanup inactive connections every 30 seconds
setInterval(() => {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

  connections.forEach((user, ws) => {
    if (user.lastSeen < fiveMinutesAgo || ws.readyState !== 1) {
      handleDisconnection(ws);
    }
  });
}, 30000);
