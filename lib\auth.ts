import type { User } from "./types"

export interface AuthCredentials {
  email: string
  password: string
  name?: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  error?: string
}

// Mock user database - in a real app this would be handled by your backend
const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Demo User",
    avatar: "/diverse-user-avatars.png",
    createdAt: new Date("2024-01-01"),
  },
]

export async function signIn(credentials: AuthCredentials): Promise<AuthResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const user = mockUsers.find((u) => u.email === credentials.email)

  if (!user) {
    return { success: false, error: "User not found" }
  }

  // In a real app, you'd verify the password hash
  if (credentials.password !== "password") {
    return { success: false, error: "Invalid password" }
  }

  return { success: true, user }
}

export async function signUp(credentials: AuthCredentials): Promise<AuthResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const existingUser = mockUsers.find((u) => u.email === credentials.email)

  if (existingUser) {
    return { success: false, error: "User already exists" }
  }

  const newUser: User = {
    id: crypto.randomUUID(),
    email: credentials.email,
    name: credentials.name || "New User",
    avatar: "/diverse-user-avatars.png",
    createdAt: new Date(),
  }

  mockUsers.push(newUser)

  return { success: true, user: newUser }
}

export async function signOut(): Promise<void> {
  // In a real app, you'd clear tokens, etc.
  await new Promise((resolve) => setTimeout(resolve, 500))
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePassword(password: string): { valid: boolean; message?: string } {
  if (password.length < 6) {
    return { valid: false, message: "Password must be at least 6 characters" }
  }
  return { valid: true }
}
