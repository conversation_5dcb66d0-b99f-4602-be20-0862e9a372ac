"use client"

import { useState, useEffect, useRef } from "react"
import { useAppStore } from "@/lib/store"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RichTextEditor } from "./rich-text-editor"
import { MarkdownEditor } from "./markdown-editor"
import { KanbanEditor } from "./kanban-editor"
import { CollaboratorAvatars } from "@/components/collaboration/collaborator-avatars"
import { CollaborationPanel } from "@/components/collaboration/collaboration-panel"
import { CollaborationCursor } from "@/components/collaboration/collaboration-cursor"
import { ShareDialog } from "@/components/sharing/share-dialog"
import { Share, Star, MoreVertical, FileText, Hash, Kanban, Users } from "lucide-react"
import { debounce } from "@/lib/utils"
import { collaborationManager, type CollaboratorPresence } from "@/lib/collaboration"
import type { Note } from "@/lib/types"

export function NoteEditor() {
  const activeNote = useAppStore((state) => state.activeNote)
  const updateNote = useAppStore((state) => state.updateNote)
  const currentUser = useAppStore((state) => state.currentUser)

  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [collaborators, setCollaborators] = useState<CollaboratorPresence[]>([])
  const [showCollaborationPanel, setShowCollaborationPanel] = useState(false)
  const [showShareDialog, setShowShareDialog] = useState(false)
  const editorRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (activeNote) {
      setTitle(activeNote.title)
      setContent(activeNote.content)

      if (currentUser) {
        collaborationManager.joinNote(activeNote.id, currentUser)
      }
    }

    return () => {
      collaborationManager.leaveNote()
    }
  }, [activeNote, currentUser])

  useEffect(() => {
    const handleCollaborationUpdate = (data: any) => {
      setCollaborators(data.collaborators || [])
    }

    const handleContentChange = (change: any) => {
      console.log("[v0] Remote content change received:", change)
    }

    collaborationManager.on("collaboration-update", handleCollaborationUpdate)
    collaborationManager.on("content-change", handleContentChange)

    return () => {
      collaborationManager.off("collaboration-update", handleCollaborationUpdate)
      collaborationManager.off("content-change", handleContentChange)
    }
  }, [])

  const debouncedUpdateNote = debounce((noteId: string, updates: Partial<Note>) => {
    updateNote(noteId, updates)
  }, 500)

  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle)
    if (activeNote) {
      debouncedUpdateNote(activeNote.id, { title: newTitle })
    }
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    if (activeNote) {
      debouncedUpdateNote(activeNote.id, { content: newContent })

      collaborationManager.broadcastContentChange({
        id: crypto.randomUUID(),
        userId: currentUser?.id || "",
        type: "insert",
        position: 0,
        content: newContent,
        timestamp: new Date(),
      })
    }
  }

  const getTypeIcon = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return <FileText className="w-4 h-4" />
      case "markdown":
        return <Hash className="w-4 h-4" />
      case "kanban":
        return <Kanban className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "markdown":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "kanban":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    }
  }

  if (!activeNote) {
    return (
      <div className="flex-1 flex items-center justify-center text-center text-muted-foreground">
        <div>
          <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-medium mb-2">No note selected</h3>
          <p className="text-sm">Select a note from the sidebar or create a new one to get started</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex-1 flex flex-col h-full" ref={editorRef}>
        <div className="border-b border-border bg-card px-6 py-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className={`${getTypeColor(activeNote.type)}`}>
                {getTypeIcon(activeNote.type)}
                <span className="ml-1 capitalize">{activeNote.type.replace("-", " ")}</span>
              </Badge>
              <span className="text-sm text-muted-foreground">
                Last edited {new Date(activeNote.updatedAt).toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <CollaboratorAvatars collaborators={collaborators} />

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => setShowCollaborationPanel(!showCollaborationPanel)}>
                  <Users className="w-4 h-4 mr-2" />
                  Collaborate
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowShareDialog(true)}>
                  <Share className="w-4 h-4 mr-2" />
                  Share
                </Button>
                <Button variant="ghost" size="sm">
                  <Star className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          <Input
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            className="text-2xl font-bold border-0 px-0 focus-visible:ring-0 bg-transparent"
            placeholder="Untitled"
          />
        </div>

        <div className="flex-1 overflow-hidden relative">
          {activeNote.type === "rich-text" && (
            <RichTextEditor note={activeNote} onContentChange={handleContentChange} />
          )}
          {activeNote.type === "markdown" && <MarkdownEditor note={activeNote} onContentChange={handleContentChange} />}
          {activeNote.type === "kanban" && <KanbanEditor note={activeNote} onContentChange={handleContentChange} />}

          {collaborators
            .filter((c) => c.cursor && c.userId !== currentUser?.id)
            .map((collaborator) => (
              <CollaborationCursor key={collaborator.userId} collaborator={collaborator} editorRef={editorRef} />
            ))}
        </div>
      </div>

      <CollaborationPanel isOpen={showCollaborationPanel} onClose={() => setShowCollaborationPanel(false)} />

      {activeNote && (
        <ShareDialog note={activeNote} isOpen={showShareDialog} onClose={() => setShowShareDialog(false)} />
      )}
    </>
  )
}
