"use client"

import type React from "react"

import { useState } from "react"
import { useAppStore } from "@/lib/store"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Plus, Search, FileText, Hash, Kanban, MoreVertical, Trash2, Share, Copy, Loader2 } from "lucide-react"
import { formatDate } from "@/lib/utils"
import type { Note } from "@/lib/types"

export function NoteSidebar() {
  const notes = useAppStore((state) => state.notes)
  const activeNote = useAppStore((state) => state.activeNote)
  const setActiveNote = useAppStore((state) => state.setActiveNote)
  const addNote = useAppStore((state) => state.addNote)
  const deleteNote = useAppStore((state) => state.deleteNote)
  const currentUser = useAppStore((state) => state.currentUser)
  const searchQuery = useAppStore((state) => state.searchQuery)
  const setSearchQuery = useAppStore((state) => state.setSearchQuery)
  const notesLoading = useAppStore((state) => state.notesLoading)
  const notesError = useAppStore((state) => state.notesError)

  const [creatingNote, setCreatingNote] = useState(false)

  const filteredNotes = notes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const createNewNote = async (type: "rich-text" | "markdown" | "kanban") => {
    if (!currentUser || creatingNote) return

    setCreatingNote(true)
    try {
      const newNote = {
        title: `New ${type === "rich-text" ? "Note" : type === "markdown" ? "Markdown" : "Kanban Board"}`,
        content:
          type === "kanban"
            ? JSON.stringify({
                columns: [
                  { id: "1", title: "To Do", cards: [], order: 0 },
                  { id: "2", title: "In Progress", cards: [], order: 1 },
                  { id: "3", title: "Done", cards: [], order: 2 },
                ],
              })
            : "",
        type,
        authorId: currentUser.id,
        collaborators: [],
        isPublic: false,
        shareSettings: {
          isPublic: false,
          allowedUsers: [],
          permissions: {},
        },
        tags: [],
      }

      await addNote(newNote)
    } finally {
      setCreatingNote(false)
    }
  }

  const handleDeleteNote = async (noteId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    await deleteNote(noteId)
  }

  const getTypeIcon = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return <FileText className="w-4 h-4" />
      case "markdown":
        return <Hash className="w-4 h-4" />
      case "kanban":
        return <Kanban className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "markdown":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "kanban":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    }
  }

  return (
    <div className="w-80 border-r border-border bg-sidebar flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-sidebar-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-sidebar-foreground">Notes</h2>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" className="h-8 w-8 p-0" disabled={creatingNote}>
                {creatingNote ? <Loader2 className="w-4 h-4 animate-spin" /> : <Plus className="w-4 h-4" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => createNewNote("rich-text")}>
                <FileText className="w-4 h-4 mr-2" />
                Rich Text Note
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => createNewNote("markdown")}>
                <Hash className="w-4 h-4 mr-2" />
                Markdown Note
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => createNewNote("kanban")}>
                <Kanban className="w-4 h-4 mr-2" />
                Kanban Board
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-sidebar-primary"
          />
        </div>
      </div>

      {/* Notes List */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-2">
          {notesLoading && (
            <div className="text-center py-8 text-muted-foreground">
              <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p className="text-sm">Loading notes...</p>
            </div>
          )}

          {notesError && (
            <div className="text-center py-8 text-destructive">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Error loading notes</p>
              <p className="text-xs mt-1">{notesError}</p>
            </div>
          )}

          {!notesLoading &&
            !notesError &&
            filteredNotes.map((note) => (
              <div
                key={note.id}
                className={`sidebar-item cursor-pointer ${
                  activeNote?.id === note.id ? "bg-sidebar-accent text-sidebar-accent-foreground" : ""
                }`}
                onClick={() => setActiveNote(note)}
              >
                <div className="flex items-start gap-3 flex-1 min-w-0">
                  <div className="flex-shrink-0 mt-0.5">{getTypeIcon(note.type)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium truncate text-sm">{note.title}</h3>
                      <Badge variant="secondary" className={`text-xs ${getTypeColor(note.type)}`}>
                        {note.type}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {note.type === "kanban" ? "Kanban Board" : note.content || "No content"}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">{formatDate(note.updatedAt)}</p>
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Share className="w-4 h-4 mr-2" />
                      Share
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Copy className="w-4 h-4 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-destructive" onClick={(e) => handleDeleteNote(note.id, e)}>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}

          {!notesLoading && !notesError && filteredNotes.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No notes found</p>
              {searchQuery && <p className="text-xs mt-1">Try adjusting your search</p>}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
