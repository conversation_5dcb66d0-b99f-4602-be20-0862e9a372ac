import mysql from "mysql2/promise"

const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  port: Number.parseInt(process.env.DB_PORT || "3306"),
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "noteapp",
  charset: "utf8mb4",
  timezone: "+00:00",
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
}

// Connection pool for better performance
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
})

export class Database {
  static async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    try {
      const [rows] = await pool.execute(sql, params)
      return rows as T[]
    } catch (error) {
      console.error("Database query error:", error)
      throw new Error("Database operation failed")
    }
  }

  static async queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
    const results = await this.query<T>(sql, params)
    return results.length > 0 ? results[0] : null
  }

  static async insert(sql: string, params?: any[]): Promise<number> {
    try {
      const [result] = await pool.execute(sql, params)
      return (result as any).insertId
    } catch (error) {
      console.error("Database insert error:", error)
      throw new Error("Database insert failed")
    }
  }

  static async update(sql: string, params?: any[]): Promise<number> {
    try {
      const [result] = await pool.execute(sql, params)
      return (result as any).affectedRows
    } catch (error) {
      console.error("Database update error:", error)
      throw new Error("Database update failed")
    }
  }

  static async transaction<T>(callback: (connection: mysql.Connection) => Promise<T>): Promise<T> {
    const connection = await pool.getConnection()
    try {
      await connection.beginTransaction()
      const result = await callback(connection)
      await connection.commit()
      return result
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Health check
  static async ping(): Promise<boolean> {
    try {
      await pool.execute("SELECT 1")
      return true
    } catch {
      return false
    }
  }
}

// Database models with proper typing
export interface User {
  id: number
  email: string
  password_hash: string
  name: string
  avatar_url?: string
  created_at: Date
  updated_at: Date
}

export interface Note {
  id: number
  title: string
  content: string
  type: "rich-text" | "markdown" | "kanban"
  owner_id: number
  folder_id?: number
  tags: string[]
  is_favorite: boolean
  created_at: Date
  updated_at: Date
}

export interface SharedNote {
  id: number
  note_id: number
  shared_by: number
  shared_with_email?: string
  permission: "view" | "edit" | "admin"
  share_token?: string
  expires_at?: Date
  is_public: boolean
  created_at: Date
}

export interface CollaborationSession {
  id: number
  note_id: number
  user_id: number
  cursor_position: any
  last_seen: Date
}
