import { NextResponse } from "next/server"
import { z } from "zod"
import { Database, type SharedNote } from "@/lib/database"
import { withAuth, type AuthenticatedRequest } from "@/lib/middleware-auth"
import { randomBytes } from "crypto"

const shareNoteSchema = z.object({
  shared_with_email: z.string().email().optional(),
  permission: z.enum(["view", "edit", "admin"]).default("view"),
  expires_at: z.string().datetime().optional(),
  is_public: z.boolean().default(false),
})

// POST /api/notes/[id]/share - Share note
export const POST = withAuth(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const noteId = Number.parseInt(params.id)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    const body = await request.json()
    const { shared_with_email, permission, expires_at, is_public } = shareNoteSchema.parse(body)

    // Check if note exists and user owns it
    const note = await Database.queryOne("SELECT * FROM notes WHERE id = ? AND owner_id = ?", [
      noteId,
      request.user.userId,
    ])

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    // Generate share token
    const shareToken = randomBytes(32).toString("hex")

    // Create share record
    const shareId = await Database.insert(
      "INSERT INTO shared_notes (note_id, shared_by, shared_with_email, permission, share_token, expires_at, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [
        noteId,
        request.user.userId,
        shared_with_email || null,
        permission,
        shareToken,
        expires_at ? new Date(expires_at) : null,
        is_public,
      ],
    )

    const sharedNote = await Database.queryOne<SharedNote>("SELECT * FROM shared_notes WHERE id = ?", [shareId])

    // Log activity
    await Database.insert("INSERT INTO activity_log (note_id, user_id, action, details) VALUES (?, ?, ?, ?)", [
      noteId,
      request.user.userId,
      "shared",
      JSON.stringify({ shared_with_email, permission, is_public }),
    ])

    return NextResponse.json({
      share: sharedNote,
      share_url: `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/shared/${shareToken}`,
      message: "Note shared successfully",
    })
  } catch (error) {
    console.error("Share note error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to share note" }, { status: 500 })
  }
})

// GET /api/notes/[id]/share - Get share information
export const GET = withAuth(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const noteId = Number.parseInt(params.id)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    // Check if note exists and user owns it
    const note = await Database.queryOne("SELECT * FROM notes WHERE id = ? AND owner_id = ?", [
      noteId,
      request.user.userId,
    ])

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    // Get all shares for this note
    const shares = await Database.query<SharedNote>(
      "SELECT * FROM shared_notes WHERE note_id = ? AND shared_by = ? ORDER BY created_at DESC",
      [noteId, request.user.userId],
    )

    return NextResponse.json({ shares })
  } catch (error) {
    console.error("Get shares error:", error)
    return NextResponse.json({ error: "Failed to fetch shares" }, { status: 500 })
  }
})
