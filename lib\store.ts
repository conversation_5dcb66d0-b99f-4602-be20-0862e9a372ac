import { create } from "zustand"
import { persist } from "zustand/middleware"
import { shallow } from "zustand/shallow"
import type { Note, User, KanbanBoard, CollaborationSession } from "./types"
import { apiClient } from "./api-client"

interface AppState {
  // User state
  currentUser: User | null
  users: User[]

  // Notes state
  notes: Note[]
  activeNote: Note | null
  notesLoading: boolean
  notesError: string | null

  // Kanban state
  kanbanBoards: KanbanBoard[]

  // Collaboration state
  collaborationSessions: CollaborationSession[]

  // UI state
  sidebarOpen: boolean
  currentView: "grid" | "list" | "kanban"
  searchQuery: string

  // Actions
  setCurrentUser: (user: User | null) => void
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signup: (email: string, password: string, name: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  loadCurrentUser: () => Promise<void>

  // Note actions - now async with API calls
  loadNotes: (params?: any) => Promise<void>
  addNote: (note: Omit<Note, "id" | "createdAt" | "updatedAt" | "version">) => Promise<void>
  updateNote: (id: string, updates: Partial<Note>) => Promise<void>
  deleteNote: (id: string) => Promise<void>
  setActiveNote: (note: Note | null) => void

  // UI actions
  toggleSidebar: () => void
  setCurrentView: (view: "grid" | "list" | "kanban") => void
  setSearchQuery: (query: string) => void
  addKanbanBoard: (board: Omit<KanbanBoard, "id">) => void
  updateKanbanBoard: (id: string, updates: Partial<KanbanBoard>) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentUser: null,
      users: [],
      notes: [],
      activeNote: null,
      notesLoading: false,
      notesError: null,
      kanbanBoards: [],
      collaborationSessions: [],
      sidebarOpen: true,
      currentView: "grid",
      searchQuery: "",

      // Authentication actions
      setCurrentUser: (user) => set({ currentUser: user }),

      login: async (email, password) => {
        const response = await apiClient.login(email, password)
        if (response.success && response.data?.user) {
          set({ currentUser: response.data.user })
          return { success: true }
        }
        return { success: false, error: response.error }
      },

      signup: async (email, password, name) => {
        const response = await apiClient.signup(email, password, name)
        if (response.success && response.data?.user) {
          set({ currentUser: response.data.user })
          return { success: true }
        }
        return { success: false, error: response.error }
      },

      logout: async () => {
        await apiClient.logout()
        set({ currentUser: null, notes: [], activeNote: null })
      },

      loadCurrentUser: async () => {
        const response = await apiClient.getCurrentUser()
        if (response.success && response.data?.user) {
          set({ currentUser: response.data.user })
        }
      },

      // Note actions with API integration
      loadNotes: async (params) => {
        set({ notesLoading: true, notesError: null })
        try {
          const response = await apiClient.getNotes(params)
          if (response.success && response.data?.notes) {
            // Convert API note format to frontend format
            const notes = response.data.notes.map((note: any) => ({
              ...note,
              id: note.id.toString(),
              createdAt: new Date(note.created_at),
              updatedAt: new Date(note.updated_at),
              version: 1, // API doesn't track versions yet
              authorId: note.owner_id.toString(),
              folderId: note.folder_id?.toString(),
              isFavorite: note.is_favorite,
            }))
            set({ notes, notesLoading: false })
          } else {
            set({ notesError: response.error || "Failed to load notes", notesLoading: false })
          }
        } catch (error) {
          set({ notesError: "Network error", notesLoading: false })
        }
      },

      addNote: async (noteData) => {
        const response = await apiClient.createNote({
          title: noteData.title,
          content: noteData.content,
          type: noteData.type,
          folder_id: noteData.folderId ? Number.parseInt(noteData.folderId) : undefined,
          tags: noteData.tags,
        })

        if (response.success && response.data?.note) {
          const newNote: Note = {
            ...response.data.note,
            id: response.data.note.id.toString(),
            createdAt: new Date(response.data.note.created_at),
            updatedAt: new Date(response.data.note.updated_at),
            version: 1,
            authorId: response.data.note.owner_id.toString(),
            folderId: response.data.note.folder_id?.toString(),
            isFavorite: response.data.note.is_favorite,
          }
          set((state) => ({ notes: [...state.notes, newNote] }))
        }
      },

      updateNote: async (id, updates) => {
        const response = await apiClient.updateNote(Number.parseInt(id), {
          title: updates.title,
          content: updates.content,
          type: updates.type,
          folder_id: updates.folderId ? Number.parseInt(updates.folderId) : null,
          tags: updates.tags,
          is_favorite: updates.isFavorite,
        })

        if (response.success && response.data?.note) {
          const updatedNote: Note = {
            ...response.data.note,
            id: response.data.note.id.toString(),
            createdAt: new Date(response.data.note.created_at),
            updatedAt: new Date(response.data.note.updated_at),
            version: 1,
            authorId: response.data.note.owner_id.toString(),
            folderId: response.data.note.folder_id?.toString(),
            isFavorite: response.data.note.is_favorite,
          }

          set((state) => ({
            notes: state.notes.map((note) => (note.id === id ? updatedNote : note)),
            activeNote: state.activeNote?.id === id ? updatedNote : state.activeNote,
          }))
        }
      },

      deleteNote: async (id) => {
        const response = await apiClient.deleteNote(Number.parseInt(id))
        if (response.success) {
          set((state) => ({
            notes: state.notes.filter((note) => note.id !== id),
            activeNote: state.activeNote?.id === id ? null : state.activeNote,
          }))
        }
      },

      setActiveNote: (note) => set({ activeNote: note }),
      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      setCurrentView: (view) => set({ currentView: view }),
      setSearchQuery: (query) => set({ searchQuery: query }),

      addKanbanBoard: (boardData) => {
        const newBoard: KanbanBoard = {
          ...boardData,
          id: crypto.randomUUID(),
        }
        set((state) => ({ kanbanBoards: [...state.kanbanBoards, newBoard] }))
      },

      updateKanbanBoard: (id, updates) => {
        set((state) => ({
          kanbanBoards: state.kanbanBoards.map((board) => (board.id === id ? { ...board, ...updates } : board)),
        }))
      },
    }),
    {
      name: "note-app-storage",
    },
  ),
)

export { shallow }
