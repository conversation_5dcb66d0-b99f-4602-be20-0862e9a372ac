import { NextResponse } from "next/server"
import { z } from "zod"
import { Database, type Note } from "@/lib/database"
import { withAuth, type AuthenticatedRequest } from "@/lib/middleware-auth"

const updateNoteSchema = z.object({
  title: z.string().min(1, "Title is required").max(500, "Title too long").optional(),
  content: z.string().optional(),
  type: z.enum(["rich-text", "markdown", "kanban"]).optional(),
  folder_id: z.number().nullable().optional(),
  tags: z.array(z.string()).optional(),
  is_favorite: z.boolean().optional(),
})

// GET /api/notes/[id] - Get specific note
export const GET = withAuth(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const noteId = Number.parseInt(params.id)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    const note = await Database.queryOne<Note & { folder_name?: string }>(
      `
      SELECT n.*, f.name as folder_name 
      FROM notes n 
      LEFT JOIN folders f ON n.folder_id = f.id 
      WHERE n.id = ? AND n.owner_id = ?
    `,
      [noteId, request.user.userId],
    )

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    // Parse JSON tags
    note.tags = typeof note.tags === "string" ? JSON.parse(note.tags) : note.tags || []

    return NextResponse.json({ note })
  } catch (error) {
    console.error("Get note error:", error)
    return NextResponse.json({ error: "Failed to fetch note" }, { status: 500 })
  }
})

// PUT /api/notes/[id] - Update note
export const PUT = withAuth(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const noteId = Number.parseInt(params.id)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    const body = await request.json()
    const updates = updateNoteSchema.parse(body)

    // Check if note exists and user owns it
    const existingNote = await Database.queryOne<Note>("SELECT * FROM notes WHERE id = ? AND owner_id = ?", [
      noteId,
      request.user.userId,
    ])

    if (!existingNote) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    // Build dynamic update query
    const updateFields: string[] = []
    const updateValues: any[] = []

    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        if (key === "tags") {
          updateFields.push("tags = ?")
          updateValues.push(JSON.stringify(value))
        } else {
          updateFields.push(`${key} = ?`)
          updateValues.push(value)
        }
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json({ error: "No fields to update" }, { status: 400 })
    }

    updateFields.push("updated_at = CURRENT_TIMESTAMP")
    updateValues.push(noteId, request.user.userId)

    const sql = `UPDATE notes SET ${updateFields.join(", ")} WHERE id = ? AND owner_id = ?`
    await Database.update(sql, updateValues)

    // Log activity
    await Database.insert("INSERT INTO activity_log (note_id, user_id, action, details) VALUES (?, ?, ?, ?)", [
      noteId,
      request.user.userId,
      "updated",
      JSON.stringify(updates),
    ])

    // Get updated note
    const updatedNote = await Database.queryOne<Note>("SELECT * FROM notes WHERE id = ?", [noteId])

    if (updatedNote) {
      // Parse JSON tags
      updatedNote.tags = typeof updatedNote.tags === "string" ? JSON.parse(updatedNote.tags) : updatedNote.tags || []
    }

    return NextResponse.json({ note: updatedNote, message: "Note updated successfully" })
  } catch (error) {
    console.error("Update note error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to update note" }, { status: 500 })
  }
})

// DELETE /api/notes/[id] - Delete note
export const DELETE = withAuth(async (request: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const noteId = Number.parseInt(params.id)
    if (Number.isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    // Check if note exists and user owns it
    const note = await Database.queryOne<Note>("SELECT * FROM notes WHERE id = ? AND owner_id = ?", [
      noteId,
      request.user.userId,
    ])

    if (!note) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    // Delete note (cascade will handle related records)
    await Database.update("DELETE FROM notes WHERE id = ? AND owner_id = ?", [noteId, request.user.userId])

    // Log activity
    await Database.insert("INSERT INTO activity_log (note_id, user_id, action, details) VALUES (?, ?, ?, ?)", [
      noteId,
      request.user.userId,
      "deleted",
      JSON.stringify({ title: note.title }),
    ])

    return NextResponse.json({ message: "Note deleted successfully" })
  } catch (error) {
    console.error("Delete note error:", error)
    return NextResponse.json({ error: "Failed to delete note" }, { status: 500 })
  }
})
