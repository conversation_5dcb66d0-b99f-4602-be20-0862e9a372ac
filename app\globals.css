@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens to match the professional note-taking app design brief */
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.35 0.01 258.34); /* #475569 */
  --card: oklch(0.96 0.01 258.34); /* #f1f5f9 */
  --card-foreground: oklch(0.35 0.01 258.34); /* #475569 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.35 0.01 258.34); /* #475569 */
  --primary: oklch(0.45 0.15 162.4); /* #059669 emerald-600 */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.55 0.15 162.4); /* #10b981 emerald-500 */
  --secondary-foreground: oklch(1 0 0); /* #ffffff */
  --muted: oklch(0.96 0.01 258.34); /* #f1f5f9 */
  --muted-foreground: oklch(0.45 0.01 258.34); /* #6b7280 */
  --accent: oklch(0.55 0.15 162.4); /* #10b981 */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.5 0.22 15.34); /* #be123c */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.9 0.01 258.34); /* #e5e7eb */
  --input: oklch(1 0 0); /* #ffffff */
  --ring: oklch(0.45 0.15 162.4 / 0.2); /* emerald with opacity */
  --chart-1: oklch(0.45 0.15 162.4); /* #059669 */
  --chart-2: oklch(0.55 0.15 162.4); /* #10b981 */
  --chart-3: oklch(0.96 0.01 258.34); /* #f1f5f9 */
  --chart-4: oklch(0.45 0.01 258.34); /* #6b7280 */
  --chart-5: oklch(0.5 0.22 15.34); /* #be123c */
  --radius: 0.5rem;
  --sidebar: oklch(0.96 0.01 258.34); /* #f1f5f9 */
  --sidebar-foreground: oklch(0.35 0.01 258.34); /* #475569 */
  --sidebar-primary: oklch(1 0 0); /* #ffffff */
  --sidebar-primary-foreground: oklch(0.35 0.01 258.34); /* #475569 */
  --sidebar-accent: oklch(0.55 0.15 162.4); /* #10b981 */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.9 0.01 258.34); /* #e5e7eb */
  --sidebar-ring: oklch(0.45 0.15 162.4 / 0.2); /* emerald with opacity */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.55 0.15 162.4); /* Lighter emerald for dark mode */
  --primary-foreground: oklch(0.145 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.55 0.15 162.4); /* Emerald accent */
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.55 0.15 162.4);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.55 0.15 162.4);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added organized Tailwind CSS structure as requested */
@layer components {
  .note-card {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-3 py-2 rounded-md text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors;
  }

  .editor-toolbar {
    @apply flex items-center gap-2 p-2 border-b border-border bg-muted/50;
  }

  .kanban-column {
    @apply bg-muted/30 rounded-lg p-4 min-h-96 w-80;
  }

  .collaboration-indicator {
    @apply inline-flex items-center gap-1 px-2 py-1 rounded-full bg-accent/10 text-accent text-xs font-medium;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }
}
