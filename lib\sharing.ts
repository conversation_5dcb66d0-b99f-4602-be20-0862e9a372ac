import type { ShareSettings } from "./types"

export interface ShareLink {
  id: string
  noteId: string
  token: string
  permissions: "view" | "edit" | "admin"
  expiresAt?: Date
  createdAt: Date
  createdBy: string
  isActive: boolean
  accessCount: number
}

export interface ShareInvitation {
  id: string
  noteId: string
  invitedEmail: string
  permissions: "view" | "edit" | "admin"
  invitedBy: string
  status: "pending" | "accepted" | "declined"
  createdAt: Date
  expiresAt?: Date
}

export class SharingManager {
  private shareLinks: Map<string, ShareLink> = new Map()
  private invitations: Map<string, ShareInvitation> = new Map()

  generateShareLink(
    noteId: string,
    permissions: "view" | "edit" | "admin",
    userId: string,
    expiresIn?: number,
  ): ShareLink {
    const token = this.generateSecureToken()
    const shareLink: ShareLink = {
      id: crypto.randomUUID(),
      noteId,
      token,
      permissions,
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn) : undefined,
      createdAt: new Date(),
      createdBy: userId,
      isActive: true,
      accessCount: 0,
    }

    this.shareLinks.set(shareLink.id, shareLink)
    return shareLink
  }

  getShareLink(token: string): ShareLink | null {
    for (const link of this.shareLinks.values()) {
      if (link.token === token && link.isActive) {
        if (link.expiresAt && link.expiresAt < new Date()) {
          link.isActive = false
          return null
        }
        link.accessCount++
        return link
      }
    }
    return null
  }

  revokeShareLink(linkId: string): boolean {
    const link = this.shareLinks.get(linkId)
    if (link) {
      link.isActive = false
      return true
    }
    return false
  }

  getShareLinksForNote(noteId: string): ShareLink[] {
    return Array.from(this.shareLinks.values()).filter((link) => link.noteId === noteId && link.isActive)
  }

  inviteUserByEmail(
    noteId: string,
    email: string,
    permissions: "view" | "edit" | "admin",
    invitedBy: string,
  ): ShareInvitation {
    const invitation: ShareInvitation = {
      id: crypto.randomUUID(),
      noteId,
      invitedEmail: email,
      permissions,
      invitedBy,
      status: "pending",
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    }

    this.invitations.set(invitation.id, invitation)

    // Simulate sending email invitation
    this.sendInvitationEmail(invitation)

    return invitation
  }

  acceptInvitation(invitationId: string): ShareInvitation | null {
    const invitation = this.invitations.get(invitationId)
    if (invitation && invitation.status === "pending") {
      invitation.status = "accepted"
      return invitation
    }
    return null
  }

  declineInvitation(invitationId: string): ShareInvitation | null {
    const invitation = this.invitations.get(invitationId)
    if (invitation && invitation.status === "pending") {
      invitation.status = "declined"
      return invitation
    }
    return null
  }

  getInvitationsForNote(noteId: string): ShareInvitation[] {
    return Array.from(this.invitations.values()).filter((inv) => inv.noteId === noteId)
  }

  getUserPermissions(noteId: string, userId: string, shareSettings: ShareSettings): "view" | "edit" | "admin" | null {
    // Check if user is the author
    // Note: In a real app, you'd check against the note's authorId

    // Check direct permissions
    if (shareSettings.permissions[userId]) {
      return shareSettings.permissions[userId]
    }

    // Check if note is public
    if (shareSettings.isPublic) {
      return "view"
    }

    // Check if user is in allowed users list
    if (shareSettings.allowedUsers.includes(userId)) {
      return shareSettings.permissions[userId] || "view"
    }

    return null
  }

  canUserPerformAction(
    action: "view" | "edit" | "delete" | "share" | "admin",
    userPermission: "view" | "edit" | "admin" | null,
  ): boolean {
    if (!userPermission) return false

    const permissionLevels = {
      view: ["view"],
      edit: ["view", "edit"],
      admin: ["view", "edit", "delete", "share", "admin"],
    }

    const allowedActions = permissionLevels[userPermission] || []
    return allowedActions.includes(action)
  }

  private generateSecureToken(): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    let result = ""
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  private sendInvitationEmail(invitation: ShareInvitation): void {
    // Simulate email sending
    console.log(`[v0] Sending invitation email to ${invitation.invitedEmail}`)
    console.log(`[v0] Invitation link: /invite/${invitation.id}`)
  }

  getPublicShareUrl(noteId: string, token: string): string {
    return `${window.location.origin}/shared/${token}`
  }
}

export const sharingManager = new SharingManager()
