"use client"

import { useState } from "react"
import { LoginForm } from "./login-form"
import { SignupForm } from "./signup-form"
import { FileText, Users, Shield, Zap } from "lucide-react"

export function AuthPage() {
  const [isLogin, setIsLogin] = useState(true)

  const toggleMode = () => setIsLogin(!isLogin)

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-8 items-center">
        {/* Left side - Branding and features */}
        <div className="hidden lg:block space-y-8">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-primary-foreground" />
              </div>
              <h1 className="text-3xl font-bold text-balance">NoteTaker Pro</h1>
            </div>
            <p className="text-xl text-muted-foreground text-pretty">
              The professional note-taking app that grows with your ideas
            </p>
          </div>

          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <FileText className="w-4 h-4 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Multiple Note Formats</h3>
                <p className="text-sm text-muted-foreground">Rich text, Markdown, and Kanban boards all in one place</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Users className="w-4 h-4 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Real-time Collaboration</h3>
                <p className="text-sm text-muted-foreground">Work together with your team in real-time</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield className="w-4 h-4 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Secure Sharing</h3>
                <p className="text-sm text-muted-foreground">Control who can view and edit your notes</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Zap className="w-4 h-4 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Powerful Search</h3>
                <p className="text-sm text-muted-foreground">Find any note instantly with full-text search</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Auth forms */}
        <div className="w-full">
          {isLogin ? <LoginForm onToggleMode={toggleMode} /> : <SignupForm onToggleMode={toggleMode} />}

          {/* Demo credentials hint */}
          {isLogin && (
            <div className="mt-4 p-3 bg-muted/50 rounded-lg text-center text-sm text-muted-foreground">
              <strong>Demo credentials:</strong> <EMAIL> / password
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
