"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { FileText, Hash, Kanban, Lock, Eye, Edit, ExternalLink } from "lucide-react"
import { sharingManager, type ShareLink } from "@/lib/sharing"
import { useAppStore } from "@/lib/store"
import type { Note } from "@/lib/types"

export default function SharedNotePage() {
  const params = useParams()
  const token = params.token as string

  const [shareLink, setShareLink] = useState<ShareLink | null>(null)
  const [note, setNote] = useState<Note | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const notes = useAppStore((state) => state.notes)

  useEffect(() => {
    if (token) {
      loadSharedNote()
    }
  }, [token])

  const loadSharedNote = () => {
    setLoading(true)
    setError(null)

    // Get share link by token
    const link = sharingManager.getShareLink(token)

    if (!link) {
      setError("This share link is invalid or has expired.")
      setLoading(false)
      return
    }

    // Find the note
    const foundNote = notes.find((n) => n.id === link.noteId)

    if (!foundNote) {
      setError("The shared note could not be found.")
      setLoading(false)
      return
    }

    setShareLink(link)
    setNote(foundNote)
    setLoading(false)
  }

  const getTypeIcon = (type: Note["type"]) => {
    switch (type) {
      case "rich-text":
        return <FileText className="w-5 h-5" />
      case "markdown":
        return <Hash className="w-5 h-5" />
      case "kanban":
        return <Kanban className="w-5 h-5" />
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "view":
        return <Eye className="w-4 h-4" />
      case "edit":
        return <Edit className="w-4 h-4" />
      default:
        return <Eye className="w-4 h-4" />
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "view":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "edit":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const renderNoteContent = () => {
    if (!note) return null

    switch (note.type) {
      case "rich-text":
        return <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: note.content }} />
      case "markdown":
        // Simple markdown rendering (same as in MarkdownEditor)
        const markdownToHtml = (markdown: string): string => {
          return markdown
            .replace(/^### (.*$)/gim, "<h3>$1</h3>")
            .replace(/^## (.*$)/gim, "<h2>$1</h2>")
            .replace(/^# (.*$)/gim, "<h1>$1</h1>")
            .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
            .replace(/\*(.*)\*/gim, "<em>$1</em>")
            .replace(/!\[([^\]]*)\]$$([^)]*)$$/gim, '<img alt="$1" src="$2" />')
            .replace(/\[([^\]]*)\]$$([^)]*)$$/gim, '<a href="$2">$1</a>')
            .replace(/\n$/gim, "<br />")
            .replace(/^> (.*$)/gim, "<blockquote>$1</blockquote>")
            .replace(/^\* (.*$)/gim, "<ul><li>$1</li></ul>")
            .replace(/^1\. (.*$)/gim, "<ol><li>$1</li></ol>")
            .replace(/`([^`]*)`/gim, "<code>$1</code>")
            .replace(/```([^```]*)```/gim, "<pre><code>$1</code></pre>")
        }

        return (
          <div
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ __html: markdownToHtml(note.content) }}
          />
        )
      case "kanban":
        try {
          const kanbanData = JSON.parse(note.content)
          return (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Kanban Board</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {kanbanData.columns?.map((column: any) => (
                  <Card key={column.id} className="kanban-column">
                    <CardHeader className="pb-3">
                      <h4 className="font-medium">{column.title}</h4>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {column.cards?.map((card: any) => (
                        <Card key={card.id} className="p-3">
                          <h5 className="font-medium text-sm mb-1">{card.title}</h5>
                          {card.description && <p className="text-xs text-muted-foreground mb-2">{card.description}</p>}
                          <Badge variant="secondary" className="text-xs">
                            {card.priority}
                          </Badge>
                        </Card>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )
        } catch (error) {
          return <p className="text-muted-foreground">Unable to display kanban board</p>
        }
      default:
        return <p className="text-muted-foreground">Unsupported note type</p>
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading shared note...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Lock className="w-5 h-5 text-destructive" />
              <h1 className="text-lg font-semibold">Access Denied</h1>
            </div>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button className="w-full mt-4" onClick={() => (window.location.href = "/")}>
              <ExternalLink className="w-4 h-4 mr-2" />
              Go to NoteTaker Pro
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-bold">NoteTaker Pro</h1>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Lock className="w-3 h-3" />
                Shared Note
              </Badge>
              <Button variant="outline" size="sm" onClick={() => (window.location.href = "/")}>
                <ExternalLink className="w-4 h-4 mr-2" />
                Open App
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {note && shareLink && (
          <div className="space-y-6">
            {/* Note Header */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                {getTypeIcon(note.type)}
                <h1 className="text-3xl font-bold text-balance">{note.title}</h1>
              </div>

              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <Badge variant="secondary" className={getPermissionColor(shareLink.permissions)}>
                  {getPermissionIcon(shareLink.permissions)}
                  <span className="ml-1 capitalize">{shareLink.permissions} access</span>
                </Badge>
                <span>Last updated {new Date(note.updatedAt).toLocaleString()}</span>
              </div>
            </div>

            {/* Note Content */}
            <Card>
              <CardContent className="p-6">{renderNoteContent()}</CardContent>
            </Card>

            {/* Footer */}
            <div className="text-center text-sm text-muted-foreground">
              <p>This note was shared via NoteTaker Pro</p>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
